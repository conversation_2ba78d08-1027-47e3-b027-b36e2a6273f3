import 'reflect-metadata';
import { serve } from '@hono/node-server';
import { loadConfig } from '@procuregpt/config';
import { diContainer } from '@procuregpt/shared';
import { createApp } from './app';

async function startServer() {
  try {
    // Load configuration
    const config = loadConfig();
    
    // Initialize DI container
    await diContainer.initializeAll(config);
    
    // Create Hono app
    const app = await createApp();
    
    // Start server
    const port = config.port;
    console.log(`🚀 Starting ProcureGPT API server on port ${port}...`);
    
    serve({
      fetch: app.fetch,
      port,
    }, (info) => {
      console.log(`✅ Server running on http://localhost:${info.port}`);
      console.log(`📚 API documentation available at http://localhost:${info.port}/docs`);
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Received SIGINT, shutting down gracefully...');
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
