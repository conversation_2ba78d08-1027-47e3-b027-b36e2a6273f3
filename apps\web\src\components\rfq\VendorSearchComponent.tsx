import { useState, useEffect } from "react";
import { Search, Mail, Phone, MapPin, Building, Globe, Star, Plus, Sparkles } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useRFQStore } from "@/store/rfqStore";
import type { Vendor } from "@/types/p2p";

interface VendorSearchComponentProps {
  onVendorSelect?: (vendor: Vendor) => void;
  onMultipleSelect?: (vendors: Vendor[]) => void;
  multiSelect?: boolean;
  showSelected?: boolean;
  className?: string;
}

export function VendorSearchComponent({
  onVendorSelect,
  onMultipleSelect,
  multiSelect = false,
  showSelected = true,
  className = ""
}: VendorSearchComponentProps) {
  const {
    vendorSearch,
    searchVendors,
    clearVendorSearch,
    selectVendor,
    unselectVendor,
    clearSelectedVendors,
    setSearchQuery
  } = useRFQStore();

  const [localQuery, setLocalQuery] = useState(vendorSearch.searchQuery);

  useEffect(() => {
    if (onMultipleSelect && multiSelect) {
      onMultipleSelect(vendorSearch.selectedVendors);
    }
  }, [vendorSearch.selectedVendors, onMultipleSelect, multiSelect]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      await searchVendors(query.trim());
    } else {
      clearVendorSearch();
    }
  };

  const handleVendorClick = (vendor: Vendor) => {
    if (multiSelect) {
      const isSelected = vendorSearch.selectedVendors.some(v => v.id === vendor.id);
      if (isSelected) {
        unselectVendor(vendor.id);
      } else {
        selectVendor(vendor);
      }
    } else {
      onVendorSelect?.(vendor);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(localQuery);
  };

  const isVendorSelected = (vendor: Vendor) => {
    return vendorSearch.selectedVendors.some(v => v.id === vendor.id);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Input */}
      <form onSubmit={handleSearchSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vendors by name, email, phone, company..."
            value={localQuery}
            onChange={(e) => setLocalQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button type="submit" disabled={vendorSearch.isSearching}>
          {vendorSearch.isSearching ? "Searching..." : "Search"}
        </Button>
        {vendorSearch.searchResults && (
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setLocalQuery("");
              clearVendorSearch();
            }}
          >
            Clear
          </Button>
        )}
      </form>

      {/* Selected Vendors (if multiSelect and showSelected) */}
      {multiSelect && showSelected && vendorSearch.selectedVendors.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">
                Selected Vendors ({vendorSearch.selectedVendors.length})
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSelectedVendors}
              >
                Clear All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {vendorSearch.selectedVendors.map((vendor) => (
                <Badge
                  key={vendor.id}
                  variant="secondary"
                  className="flex items-center gap-1 px-2 py-1"
                >
                  {vendor.name}
                  <button
                    onClick={() => unselectVendor(vendor.id)}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {vendorSearch.searchResults && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">
              Search Results ({vendorSearch.searchResults.total} found)
              {vendorSearch.searchResults.query && (
                <span className="text-muted-foreground font-normal">
                  {" "}for "{vendorSearch.searchResults.query}"
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {vendorSearch.searchResults.vendors.map((vendor) => (
                  <VendorCard
                    key={vendor.id}
                    vendor={vendor}
                    isSelected={isVendorSelected(vendor)}
                    multiSelect={multiSelect}
                    onClick={() => handleVendorClick(vendor)}
                  />
                ))}
                {vendorSearch.searchResults.vendors.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No vendors found matching your search.</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!vendorSearch.searchResults && !vendorSearch.isSearching && (
        <div className="text-center text-muted-foreground py-12">
          <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Search for vendors to get started.</p>
          <p className="text-sm">You can search by name, email, phone, company, or address.</p>
        </div>
      )}
    </div>
  );
}

interface VendorCardProps {
  vendor: Vendor;
  isSelected: boolean;
  multiSelect: boolean;
  onClick: () => void;
}

function VendorCard({ vendor, isSelected, multiSelect, onClick }: VendorCardProps) {
  return (
    <div
      className={`border rounded-lg p-4 cursor-pointer transition-colors hover:bg-muted/50 ${
        isSelected ? "bg-primary/10 border-primary" : ""
      }`}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        {multiSelect && (
          <Checkbox
            checked={isSelected}
            onChange={() => {}} // Handled by parent onClick
            className="mt-1"
          />
        )}
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <h3 className="font-medium">{vendor.name}</h3>
            {vendor.favorite && (
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
            )}
            {vendor.pendingVerification && (
              <Badge variant="secondary" className="text-xs flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                Auto-discovered
              </Badge>
            )}
          </div>

          {vendor.company && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Building className="h-4 w-4" />
              {vendor.company}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            {vendor.email && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span className="truncate">{vendor.email}</span>
              </div>
            )}

            {vendor.phone && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Phone className="h-4 w-4" />
                {vendor.phone}
              </div>
            )}

            {vendor.mobile && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span className="text-xs bg-muted px-1 rounded">Mobile:</span>
                {vendor.mobile}
              </div>
            )}

            {vendor.website && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Globe className="h-4 w-4" />
                <span className="truncate">{vendor.website}</span>
              </div>
            )}
          </div>

          {vendor.address && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span className="truncate">{vendor.address}</span>
            </div>
          )}

          {vendor.notes && (
            <div className="text-sm text-muted-foreground bg-muted/30 p-2 rounded">
              {vendor.notes}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
