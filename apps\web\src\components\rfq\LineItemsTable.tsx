import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import type { RFQLineItem } from "@/store/rfqStore";
import { LineItemRow } from "./LineItemRow";

export function LineItemsTable({ items, onChange, onAdd, onRemove }: { items: RFQLineItem[]; onChange: (id: string, patch: Partial<RFQLineItem>) => void; onAdd: () => void; onRemove: (id: string) => void }) {
  return (
    <div className="border rounded-md overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Item</TableHead>
            <TableHead>Part #</TableHead>
            <TableHead className="w-24">Qty</TableHead>
            <TableHead className="w-28">Unit</TableHead>
            <TableHead>Material/Spec</TableHead>
            <TableHead className="w-40">Due</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead className="w-28 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((it) => (
            <LineItemRow key={it.id} item={it} onChange={(patch) => onChange(it.id, patch)} onRemove={() => onRemove(it.id)} />
          ))}
        </TableBody>
      </Table>
      <div className="p-2">
        <Button variant="outline" onClick={onAdd}>Add Line Item</Button>
      </div>
    </div>
  );
}

