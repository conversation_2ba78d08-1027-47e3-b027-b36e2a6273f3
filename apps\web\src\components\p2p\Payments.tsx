import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { PaymentTransaction, UUID } from "@/types/p2p";

export type PaymentsProps = {
  payments: PaymentTransaction[];
  onSchedule: (p: PaymentTransaction) => void;
  onStatus: (id: UUID, status: PaymentTransaction["status"]) => void;
};

export function Payments({ payments, onSchedule, onStatus }: PaymentsProps) {
  const [form, setForm] = useState<{ invoiceId: string; method: PaymentTransaction["method"] }>(() => ({ invoiceId: "", method: "NEFT" }));

  function handleSchedule(e: React.FormEvent) {
    e.preventDefault();
    if (!form.invoiceId) return;
    onSchedule({ id: crypto.randomUUID(), invoiceId: form.invoiceId, method: form.method, status: "scheduled" });
    setForm({ invoiceId: "", method: "NEFT" });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payments</CardTitle>
        <CardDescription>Schedule and track payments</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSchedule} className="grid grid-cols-1 md:grid-cols-5 gap-2">
          <Input placeholder="Invoice ID" value={form.invoiceId} onChange={e => setForm({ ...form, invoiceId: e.target.value })} />
          <select value={form.method} onChange={e => setForm({ ...form, method: e.target.value as any })} className="border rounded-md px-2 py-1 bg-transparent">
            <option value="NEFT">NEFT</option>
            <option value="RTGS">RTGS</option>
            <option value="UPI">UPI</option>
            <option value="ACH">ACH</option>
            <option value="Wire">Wire</option>
          </select>
          <Button type="submit">Schedule</Button>
        </form>

        <div className="space-y-2">
          {payments.map(p => (
            <div key={p.id} className="border rounded-md p-3 flex items-center justify-between">
              <div className="text-sm">Invoice {p.invoiceId} • {p.method}</div>
              <select value={p.status} onChange={e => onStatus(p.id, e.target.value as any)} className="border rounded-md px-2 py-1 bg-transparent">
                <option value="scheduled">Scheduled</option>
                <option value="executed">Executed</option>
                <option value="failed">Failed</option>
                <option value="reconciled">Reconciled</option>
              </select>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

