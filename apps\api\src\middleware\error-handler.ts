import { Context } from 'hono';
import { ProcureGPTError, isOperationalError, toProcureGPTError } from '@procuregpt/shared';
import { diContainer, TOKENS } from '@procuregpt/shared';
import type { Logger } from '@procuregpt/shared';

export async function errorHandler(error: Error, c: Context) {
  const logger = diContainer.resolve<Logger>(TOKENS.LOGGER);
  
  // Convert to ProcureGPTError if needed
  const procureError = toProcureGPTError(error);
  
  // Log error
  if (isOperationalError(procureError)) {
    logger.warn('Operational error occurred', {
      error: procureError.message,
      code: procureError.code,
      statusCode: procureError.statusCode,
      path: c.req.path,
      method: c.req.method,
    });
  } else {
    logger.error('Unexpected error occurred', procureError, {
      path: c.req.path,
      method: c.req.method,
      stack: procureError.stack,
    });
  }

  // Return error response
  return c.json({
    success: false,
    error: procureError.message,
    code: procureError.code,
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === 'development' && {
      stack: procureError.stack,
    }),
  }, procureError.statusCode);
}
