{"name": "procuregpt", "private": true, "version": "1.0.0", "type": "module", "packageManager": "pnpm@8.15.0", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "test:unit": "turbo test:unit", "test:integration": "turbo test:integration", "type-check": "turbo type-check", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo build --filter=!@procuregpt/docs && changeset publish", "dev:api": "cd apps/api && pnpm dev", "dev:workers": "cd apps/workers && pnpm dev", "dev:web": "cd apps/web && pnpm dev", "start:api": "cd apps/api && pnpm start", "start:workers": "cd apps/workers && pnpm start", "demo:queue": "tsx scripts/demo-job-queue.ts"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@turbo/gen": "^1.12.4", "prettier": "^3.2.5", "turbo": "^1.12.4", "typescript": "^5.3.3", "tsx": "^4.7.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}