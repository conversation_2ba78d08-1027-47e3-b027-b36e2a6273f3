import { Hono } from 'hono';
import { z } from 'zod';

const app = new Hono();

// Schema for vendor discovery request
const VendorDiscoveryRequestSchema = z.object({
  vendorNames: z.array(z.string()),
  location: z.string().optional(),
  includeContactDetails: z.boolean().default(true),
});

// Schema for discovered vendor information
const DiscoveredVendorSchema = z.object({
  name: z.string(),
  company: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  website: z.string().optional(),
  address: z.string().optional(),
  confidence: z.number(),
  source: z.string(),
});

function ok<T>(data: T, status = 200) {
  return new Response(JSON.stringify(data), { 
    status, 
    headers: { 'Content-Type': 'application/json' } 
  });
}

function bad(message: string, status = 400) {
  return new Response(JSON.stringify({ error: message }), { 
    status, 
    headers: { 'Content-Type': 'application/json' } 
  });
}

/**
 * Extract vendor names from text using pattern matching
 */
function extractVendorNames(text: string): string[] {
  const vendorPatterns = [
    // Pattern: "To: Vendor Name"
    /To:\s*([A-Z][a-zA-Z\s&.-]+(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)?\.?)/gi,
    // Pattern: "Vendor: Company Name"  
    /Vendor:\s*([A-Z][a-zA-Z\s&.-]+(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)?\.?)/gi,
    // Pattern: Company names with common suffixes
    /([A-Z][a-zA-Z\s&.-]+(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)\.?)/g,
    // Pattern: "Supplier: Company Name"
    /Supplier:\s*([A-Z][a-zA-Z\s&.-]+)/gi,
  ];

  const vendors = new Set<string>();
  
  for (const pattern of vendorPatterns) {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      if (match[1]) {
        const vendorName = match[1].trim();
        if (isValidVendorName(vendorName)) {
          vendors.add(vendorName);
        }
      }
    }
  }

  return Array.from(vendors);
}

/**
 * Validate if extracted text is likely a vendor name
 */
function isValidVendorName(name: string): boolean {
  const invalidPatterns = [
    /^(the|and|or|of|in|at|to|for|with|by|from)$/i,
    /^(january|february|march|april|may|june|july|august|september|october|november|december)$/i,
    /^(monday|tuesday|wednesday|thursday|friday|saturday|sunday)$/i,
    /^\d+$/,
    /^[a-z]+$/,  // All lowercase (likely not a company name)
  ];

  if (name.length < 3 || name.length > 100) return false;
  
  for (const pattern of invalidPatterns) {
    if (pattern.test(name)) return false;
  }

  return true;
}

/**
 * Search for vendor contact information using Perplexity API
 */
async function searchVendorInfo(vendorName: string, location?: string) {
  try {
    const apiKey = process.env.PERPLEXITY_API_KEY;
    if (!apiKey) {
      throw new Error('Perplexity API key not configured');
    }

    const searchQuery = `Find contact information for "${vendorName}" company including email address, phone number, and website. Focus on suppliers in ${location || 'India, particularly Mangaluru region'}.`;

    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'sonar-pro',
        messages: [
          { 
            role: 'system', 
            content: 'You are a business information researcher. Extract specific contact details including email addresses, phone numbers, and company information. Provide accurate, up-to-date information only.' 
          },
          { role: 'user', content: searchQuery }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`Perplexity API error: ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content || '';
    
    return parseContactInfo(vendorName, content);
  } catch (error) {
    console.error(`Error searching for vendor ${vendorName}:`, error);
    return null;
  }
}

/**
 * Parse AI response to extract structured contact information
 */
function parseContactInfo(vendorName: string, text: string) {
  try {
    // Extract email addresses
    const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    const emails = text.match(emailPattern) || [];

    // Extract phone numbers (Indian format)
    const phonePattern = /(\+91[-\s]?)?(\d{10}|\d{3}[-\s]?\d{3}[-\s]?\d{4})/g;
    const phones = text.match(phonePattern) || [];

    // Extract website URLs
    const websitePattern = /(https?:\/\/[^\s]+|www\.[^\s]+)/g;
    const websites = text.match(websitePattern) || [];

    // Extract company information
    const companyPattern = new RegExp(`(${vendorName}[^.]*(?:Ltd|Pvt|Inc|Corp|Company|Co|Industries|Enterprises|Solutions|Systems|Technologies|Tech|Electric|Electronics|Electricals)[^.]*)`, 'i');
    const companyMatch = text.match(companyPattern);

    if (emails.length === 0 && phones.length === 0) {
      return null; // No useful contact info found
    }

    // Calculate confidence score
    let confidence = 0;
    if (emails.length > 0) confidence += 0.4;
    if (phones.length > 0) confidence += 0.3;
    if (websites.length > 0) confidence += 0.2;
    if (emails.length > 0 && phones.length > 0) confidence += 0.1;

    return {
      name: vendorName,
      company: companyMatch ? companyMatch[0].trim() : vendorName,
      email: emails[0] || undefined,
      phone: phones[0] || undefined,
      mobile: phones[1] || undefined,
      website: websites[0] || undefined,
      confidence: Math.min(confidence, 1.0),
      source: 'Perplexity AI Search'
    };

  } catch (error) {
    console.error('Error parsing contact info:', error);
    return null;
  }
}

// Extract vendor names from text
app.post('/api/vendor-discovery/extract', async (c) => {
  try {
    const body = await c.req.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return bad('Text content is required');
    }

    const vendorNames = extractVendorNames(text);
    
    return ok({
      vendorNames,
      count: vendorNames.length,
      extractedFrom: text.substring(0, 200) + (text.length > 200 ? '...' : '')
    });

  } catch (error) {
    console.error('Vendor extraction error:', error);
    return bad('Failed to extract vendor names');
  }
});

// Discover vendor contact information
app.post('/api/vendor-discovery/search', async (c) => {
  try {
    const body = await c.req.json();
    const parsed = VendorDiscoveryRequestSchema.safeParse(body);
    
    if (!parsed.success) {
      return bad(`Invalid request: ${parsed.error.message}`);
    }

    const { vendorNames, location, includeContactDetails } = parsed.data;

    if (!includeContactDetails) {
      return ok({
        vendors: vendorNames.map(name => ({ name, source: 'Manual Input' })),
        searchQuery: vendorNames.join(', '),
        timestamp: new Date().toISOString()
      });
    }

    const discoveredVendors = [];

    for (const vendorName of vendorNames) {
      const vendorInfo = await searchVendorInfo(vendorName, location);
      if (vendorInfo) {
        discoveredVendors.push(vendorInfo);
      }
    }

    return ok({
      vendors: discoveredVendors,
      searchQuery: vendorNames.join(', ') + (location ? ` in ${location}` : ''),
      timestamp: new Date().toISOString(),
      totalRequested: vendorNames.length,
      totalFound: discoveredVendors.length
    });

  } catch (error) {
    console.error('Vendor discovery error:', error);
    return bad('Failed to discover vendor information');
  }
});

// Combined endpoint: extract and search in one call
app.post('/api/vendor-discovery/auto', async (c) => {
  try {
    const body = await c.req.json();
    const { text, location } = body;

    if (!text || typeof text !== 'string') {
      return bad('Text content is required');
    }

    // Step 1: Extract vendor names
    const vendorNames = extractVendorNames(text);
    
    if (vendorNames.length === 0) {
      return ok({
        vendors: [],
        vendorNames: [],
        message: 'No vendor names found in the provided text'
      });
    }

    // Step 2: Search for contact information
    const discoveredVendors = [];

    for (const vendorName of vendorNames) {
      const vendorInfo = await searchVendorInfo(vendorName, location);
      if (vendorInfo) {
        discoveredVendors.push(vendorInfo);
      }
    }

    return ok({
      vendors: discoveredVendors,
      vendorNames,
      searchQuery: vendorNames.join(', ') + (location ? ` in ${location}` : ''),
      timestamp: new Date().toISOString(),
      totalExtracted: vendorNames.length,
      totalFound: discoveredVendors.length
    });

  } catch (error) {
    console.error('Auto vendor discovery error:', error);
    return bad('Failed to auto-discover vendors');
  }
});

export default app;
