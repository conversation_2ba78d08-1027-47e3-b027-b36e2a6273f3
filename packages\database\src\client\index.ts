import { Pool, PoolClient } from 'pg';
import { Service, Singleton, Inject, TOKENS } from '@procuregpt/shared';
import type { DatabaseConfig } from '@procuregpt/types';
import type { Logger } from '@procuregpt/shared';

export interface DatabaseClient {
  query<T = any>(text: string, params?: any[]): Promise<T[]>;
  getClient(): Promise<PoolClient>;
  transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T>;
  close(): Promise<void>;
  ping(): Promise<boolean>;
}

@Singleton()
@Service
export class PostgreSQLClient implements DatabaseClient {
  private pool: Pool;

  constructor(
    @Inject(TOKENS.CONFIG) private config: { database: DatabaseConfig },
    @Inject(TOKENS.LOGGER) private logger: Logger
  ) {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      min: config.database.poolMin,
      max: config.database.poolMax,
      connectionString: config.database.connectionString,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
    });

    this.pool.on('error', (err) => {
      this.logger.error('Database pool error', err);
    });

    this.pool.on('connect', () => {
      this.logger.debug('New database connection established');
    });

    this.logger.info('PostgreSQL client initialized');
  }

  async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      
      this.logger.debug('Database query executed', {
        query: text.substring(0, 100),
        duration,
        rowCount: result.rowCount,
      });
      
      return result.rows;
    } catch (error) {
      const duration = Date.now() - start;
      this.logger.error('Database query failed', error as Error, {
        query: text.substring(0, 100),
        params,
        duration,
      });
      throw error;
    }
  }

  async getClient(): Promise<PoolClient> {
    return await this.pool.connect();
  }

  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async ping(): Promise<boolean> {
    try {
      await this.query('SELECT 1');
      return true;
    } catch (error) {
      this.logger.error('Database ping failed', error as Error);
      return false;
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
    this.logger.info('Database connection pool closed');
  }
}
