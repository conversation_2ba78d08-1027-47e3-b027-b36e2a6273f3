/**
 * Dependency Injection Tokens
 * 
 * These tokens are used to identify services in the DI container.
 * Using symbols ensures type safety and prevents naming conflicts.
 */

// Core Services
export const TOKENS = {
  // Configuration
  CONFIG: Symbol('Config'),
  
  // Database
  DATABASE_CLIENT: Symbol('DatabaseClient'),
  DATABASE_POOL: Symbol('DatabasePool'),
  
  // Logging
  LOGGER: Symbol('Logger'),
  
  // External APIs
  PERPLEXITY_CLIENT: Symbol('PerplexityClient'),
  OPENAI_CLIENT: Symbol('OpenAIClient'),
  
  // Email
  EMAIL_SERVICE: Symbol('EmailService'),
  EMAIL_TEMPLATE_SERVICE: Symbol('EmailTemplateService'),
  
  // Workflow Engine
  WORKFLOW_ENGINE: Symbol('WorkflowEngine'),
  WORKFLOW_EXECUTOR: Symbol('WorkflowExecutor'),
  WORKFLOW_REPOSITORY: Symbol('WorkflowRepository'),
  
  // Job Queue
  JOB_QUEUE: Symbol('JobQueue'),
  JOB_PROCESSOR: Symbol('JobProcessor'),
  
  // RAG & Vector Search
  VECTOR_STORE: Symbol('VectorStore'),
  EMBEDDING_SERVICE: Symbol('EmbeddingService'),
  DOCUMENT_PROCESSOR: Symbol('DocumentProcessor'),
  SEMANTIC_SEARCH_SERVICE: Symbol('SemanticSearchService'),
  
  // Procurement Services
  SUPPLIER_SERVICE: Symbol('SupplierService'),
  RFQ_SERVICE: Symbol('RFQService'),
  PROCUREMENT_SERVICE: Symbol('ProcurementService'),
  MARKET_RESEARCH_SERVICE: Symbol('MarketResearchService'),
  
  // Repositories
  USER_REPOSITORY: Symbol('UserRepository'),
  SUPPLIER_REPOSITORY: Symbol('SupplierRepository'),
  RFQ_REPOSITORY: Symbol('RFQRepository'),
  WORKFLOW_EXECUTION_REPOSITORY: Symbol('WorkflowExecutionRepository'),
  DOCUMENT_REPOSITORY: Symbol('DocumentRepository'),
  
  // Authentication & Authorization
  AUTH_SERVICE: Symbol('AuthService'),
  JWT_SERVICE: Symbol('JWTService'),
  PERMISSION_SERVICE: Symbol('PermissionService'),
  
  // File Management
  FILE_STORAGE_SERVICE: Symbol('FileStorageService'),
  FILE_UPLOAD_SERVICE: Symbol('FileUploadService'),
  
  // Prompt Management
  PROMPT_MANAGER: Symbol('PromptManager'),
  TEMPLATE_ENGINE: Symbol('TemplateEngine'),
  
  // Human-in-the-Loop
  APPROVAL_SERVICE: Symbol('ApprovalService'),
  NOTIFICATION_SERVICE: Symbol('NotificationService'),
  
  // Monitoring & Analytics
  METRICS_SERVICE: Symbol('MetricsService'),
  AUDIT_SERVICE: Symbol('AuditService'),
} as const;

export type TokenType = typeof TOKENS[keyof typeof TOKENS];
