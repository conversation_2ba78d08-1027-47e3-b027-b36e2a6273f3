import { Pool } from 'pg';
import { readFileSync } from 'fs';
import { join } from 'path';
import { getPool, testConnection, checkPgVectorExtension } from './config.js';

export class DatabaseMigrator {
  private pool: Pool;

  constructor() {
    this.pool = getPool();
  }

  async runMigrations(): Promise<void> {
    console.log('🗄️ Starting database migrations...');

    try {
      // Test database connection
      const isConnected = await testConnection();
      if (!isConnected) {
        throw new Error('Database connection failed');
      }

      // Check if pgvector extension is available
      const hasVector = await checkPgVectorExtension();
      if (!hasVector) {
        console.log('📦 Installing pgvector extension...');
        await this.installPgVectorExtension();
      }

      // Run schema migration
      await this.runSchemaMigration();

      // Create indexes
      await this.createIndexes();

      console.log('✅ Database migrations completed successfully');
    } catch (error) {
      console.error('❌ Database migration failed:', error);
      throw error;
    }
  }

  private async installPgVectorExtension(): Promise<void> {
    try {
      await this.pool.query('CREATE EXTENSION IF NOT EXISTS vector;');
      console.log('✅ pgvector extension installed');
    } catch (error) {
      console.error('❌ Failed to install pgvector extension:', error);
      console.log('💡 Please ensure pgvector is installed on your PostgreSQL server');
      console.log('💡 Installation instructions: https://github.com/pgvector/pgvector#installation');
      throw error;
    }
  }

  private async runSchemaMigration(): Promise<void> {
    try {
      const schemaPath = join(process.cwd(), 'app', 'api', 'database', 'schema.sql');
      const schemaSql = readFileSync(schemaPath, 'utf8');
      
      // Split by semicolon and execute each statement
      const statements = schemaSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      for (const statement of statements) {
        try {
          await this.pool.query(statement);
        } catch (error) {
          // Log but don't fail on already exists errors
          if (error instanceof Error && error.message.includes('already exists')) {
            console.log(`⚠️ Skipping existing object: ${error.message}`);
          } else {
            throw error;
          }
        }
      }

      console.log('✅ Schema migration completed');
    } catch (error) {
      console.error('❌ Schema migration failed:', error);
      throw error;
    }
  }

  private async createIndexes(): Promise<void> {
    const indexes = [
      // Basic indexes
      'CREATE INDEX IF NOT EXISTS idx_agent_results_workflow_id ON agent_results(workflow_id);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_status ON agent_results(status);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_started_at ON agent_results(started_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_input_hash ON agent_results(input_hash);',
      
      // Vector indexes (HNSW for better performance)
      'CREATE INDEX IF NOT EXISTS idx_agent_results_input_embedding ON agent_results USING hnsw (input_embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_output_embedding ON agent_results USING hnsw (output_embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_combined_embedding ON agent_results USING hnsw (combined_embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);',
      
      // Composite indexes
      'CREATE INDEX IF NOT EXISTS idx_agent_results_status_started_at ON agent_results(status, started_at DESC);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_region_started_at ON agent_results(region, started_at DESC);',
      
      // Step indexes
      'CREATE INDEX IF NOT EXISTS idx_agent_execution_steps_result_id ON agent_execution_steps(result_id);',
      'CREATE INDEX IF NOT EXISTS idx_agent_execution_steps_workflow_id ON agent_execution_steps(workflow_id);',
      'CREATE INDEX IF NOT EXISTS idx_agent_execution_steps_status ON agent_execution_steps(status);',
      
      // Tag indexes
      'CREATE INDEX IF NOT EXISTS idx_agent_result_tags_result_id ON agent_result_tags(result_id);',
      'CREATE INDEX IF NOT EXISTS idx_agent_result_tags_tag_name ON agent_result_tags(tag_name);',
      'CREATE INDEX IF NOT EXISTS idx_agent_result_tags_tag_type ON agent_result_tags(tag_type);',
      
      // JSON indexes
      'CREATE INDEX IF NOT EXISTS idx_agent_results_query_analysis ON agent_results USING GIN (query_analysis);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_recommendations ON agent_results USING GIN (recommendations);',
      'CREATE INDEX IF NOT EXISTS idx_agent_results_rfq_data ON agent_results USING GIN (rfq_data);'
    ];

    for (const indexSql of indexes) {
      try {
        await this.pool.query(indexSql);
      } catch (error) {
        if (error instanceof Error && error.message.includes('already exists')) {
          console.log(`⚠️ Index already exists, skipping`);
        } else {
          console.warn(`⚠️ Failed to create index: ${error}`);
          // Don't throw - indexes are not critical for basic functionality
        }
      }
    }

    console.log('✅ Indexes created');
  }

  async dropTables(): Promise<void> {
    console.log('🗑️ Dropping all tables...');
    
    const dropStatements = [
      'DROP TABLE IF EXISTS agent_result_tags CASCADE;',
      'DROP TABLE IF EXISTS agent_execution_steps CASCADE;',
      'DROP TABLE IF EXISTS agent_results CASCADE;',
      'DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;'
    ];

    for (const statement of dropStatements) {
      try {
        await this.pool.query(statement);
      } catch (error) {
        console.warn(`⚠️ Failed to drop: ${error}`);
      }
    }

    console.log('✅ Tables dropped');
  }

  async resetDatabase(): Promise<void> {
    console.log('🔄 Resetting database...');
    await this.dropTables();
    await this.runMigrations();
    console.log('✅ Database reset completed');
  }

  async getTableInfo(): Promise<void> {
    try {
      const tablesQuery = `
        SELECT table_name, 
               (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
        FROM information_schema.tables t
        WHERE table_schema = 'public' 
        AND table_name LIKE 'agent_%'
        ORDER BY table_name;
      `;

      const result = await this.pool.query(tablesQuery);
      
      console.log('📊 Database Table Information:');
      console.log('================================');
      
      for (const row of result.rows) {
        console.log(`📋 ${row.table_name}: ${row.column_count} columns`);
        
        // Get row count
        try {
          const countResult = await this.pool.query(`SELECT COUNT(*) as count FROM ${row.table_name}`);
          console.log(`   📊 Rows: ${countResult.rows[0].count}`);
        } catch (error) {
          console.log(`   ❌ Could not get row count: ${error}`);
        }
      }

      // Check pgvector extension
      const vectorCheck = await checkPgVectorExtension();
      console.log(`🧠 pgvector extension: ${vectorCheck ? '✅ Available' : '❌ Not available'}`);

    } catch (error) {
      console.error('❌ Failed to get table info:', error);
    }
  }

  async seedTestData(): Promise<void> {
    console.log('🌱 Seeding test data...');

    try {
      // Create a test workflow result
      const testData = {
        workflow_id: 'test-workflow-001',
        input_text: 'Find suppliers for industrial pumps in Mangaluru region',
        input_hash: 'test-hash-001',
        status: 'completed',
        query_analysis: JSON.stringify({
          type: 'supplier_search',
          category: 'industrial_equipment',
          location: 'Mangaluru, India'
        }),
        recommendations: JSON.stringify({
          suppliers: ['ABC Pumps Ltd', 'XYZ Industrial'],
          estimated_cost: '$5000-$8000',
          timeline: '2-3 weeks'
        }),
        region: 'Mangaluru, India',
        agent_version: '1.0.0',
        completed_at: new Date(),
        execution_time_ms: 45000
      };

      const insertQuery = `
        INSERT INTO agent_results (
          workflow_id, input_text, input_hash, status,
          query_analysis, recommendations, region, agent_version,
          completed_at, execution_time_ms
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        ) ON CONFLICT (workflow_id) DO NOTHING
        RETURNING id;
      `;

      const values = [
        testData.workflow_id,
        testData.input_text,
        testData.input_hash,
        testData.status,
        testData.query_analysis,
        testData.recommendations,
        testData.region,
        testData.agent_version,
        testData.completed_at,
        testData.execution_time_ms
      ];

      const result = await this.pool.query(insertQuery, values);
      
      if (result.rows.length > 0) {
        console.log('✅ Test data seeded successfully');
        
        // Add some test tags
        const resultId = result.rows[0].id;
        await this.pool.query(
          'INSERT INTO agent_result_tags (result_id, tag_name, tag_value, tag_type) VALUES ($1, $2, $3, $4) ON CONFLICT DO NOTHING',
          [resultId, 'category', 'industrial_equipment', 'auto']
        );
        await this.pool.query(
          'INSERT INTO agent_result_tags (result_id, tag_name, tag_value, tag_type) VALUES ($1, $2, $3, $4) ON CONFLICT DO NOTHING',
          [resultId, 'region', 'Mangaluru', 'auto']
        );
        
        console.log('✅ Test tags added');
      } else {
        console.log('ℹ️ Test data already exists');
      }

    } catch (error) {
      console.error('❌ Failed to seed test data:', error);
      throw error;
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const migrator = new DatabaseMigrator();
  const command = process.argv[2];

  switch (command) {
    case 'migrate':
      await migrator.runMigrations();
      break;
    case 'reset':
      await migrator.resetDatabase();
      break;
    case 'info':
      await migrator.getTableInfo();
      break;
    case 'seed':
      await migrator.seedTestData();
      break;
    case 'drop':
      await migrator.dropTables();
      break;
    default:
      console.log('Usage: node migrate.js [migrate|reset|info|seed|drop]');
      console.log('  migrate - Run database migrations');
      console.log('  reset   - Drop and recreate all tables');
      console.log('  info    - Show database table information');
      console.log('  seed    - Add test data');
      console.log('  drop    - Drop all tables');
  }

  process.exit(0);
}
