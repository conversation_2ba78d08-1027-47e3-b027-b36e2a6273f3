import { Service, Singleton, Inject, TOKENS } from '@procuregpt/shared';
import type { 
  WorkflowDefinition, 
  WorkflowExecutionContext, 
  WorkflowExecutionStatus,
  NodeExecutionResult 
} from '@procuregpt/types';
import type { Logger } from '@procuregpt/shared';
import { WorkflowExecutor } from '../executor';
import { WorkflowContextManager } from '../context';

export interface WorkflowEngine {
  executeWorkflow(
    definition: WorkflowDefinition, 
    input: Record<string, any>, 
    userId?: string
  ): Promise<WorkflowExecutionContext>;
  
  pauseWorkflow(executionId: string): Promise<void>;
  resumeWorkflow(executionId: string): Promise<void>;
  cancelWorkflow(executionId: string): Promise<void>;
  getWorkflowStatus(executionId: string): Promise<WorkflowExecutionContext>;
}

@Singleton()
@Service
export class GraphWorkflowEngine implements WorkflowEngine {
  constructor(
    @Inject(TOKENS.LOGGER) private logger: Logger,
    @Inject(TOKENS.WORKFLOW_EXECUTOR) private executor: WorkflowExecutor,
    private contextManager: WorkflowContextManager = new WorkflowContextManager()
  ) {}

  async executeWorkflow(
    definition: WorkflowDefinition,
    input: Record<string, any>,
    userId?: string
  ): Promise<WorkflowExecutionContext> {
    const executionId = this.generateExecutionId();
    
    this.logger.info('Starting workflow execution', {
      workflowId: definition.id,
      executionId,
      userId,
      nodeCount: definition.nodes.length
    });

    // Create execution context
    const context: WorkflowExecutionContext = {
      workflowId: definition.id,
      executionId,
      userId,
      input,
      variables: { ...definition.variables },
      nodeResults: {},
      startTime: new Date(),
      status: 'running',
    };

    // Store context
    this.contextManager.setContext(executionId, context);

    try {
      // Build execution graph
      const executionGraph = this.buildExecutionGraph(definition);
      
      // Execute workflow
      const result = await this.executor.execute(definition, context, executionGraph);
      
      // Update context with final result
      context.status = result.status;
      context.endTime = result.endTime;
      context.nodeResults = result.nodeResults;
      context.error = result.error;

      this.contextManager.setContext(executionId, context);

      this.logger.info('Workflow execution completed', {
        workflowId: definition.id,
        executionId,
        status: context.status,
        duration: context.endTime ? context.endTime.getTime() - context.startTime.getTime() : 0
      });

      return context;
    } catch (error) {
      this.logger.error('Workflow execution failed', error as Error, {
        workflowId: definition.id,
        executionId
      });

      context.status = 'failed';
      context.endTime = new Date();
      context.error = (error as Error).message;
      
      this.contextManager.setContext(executionId, context);
      
      throw error;
    }
  }

  async pauseWorkflow(executionId: string): Promise<void> {
    const context = this.contextManager.getContext(executionId);
    if (!context) {
      throw new Error(`Workflow execution ${executionId} not found`);
    }

    context.status = 'paused';
    this.contextManager.setContext(executionId, context);
    
    this.logger.info('Workflow paused', { executionId });
  }

  async resumeWorkflow(executionId: string): Promise<void> {
    const context = this.contextManager.getContext(executionId);
    if (!context) {
      throw new Error(`Workflow execution ${executionId} not found`);
    }

    if (context.status !== 'paused') {
      throw new Error(`Cannot resume workflow in status: ${context.status}`);
    }

    context.status = 'running';
    this.contextManager.setContext(executionId, context);
    
    this.logger.info('Workflow resumed', { executionId });
    
    // TODO: Resume execution from where it left off
  }

  async cancelWorkflow(executionId: string): Promise<void> {
    const context = this.contextManager.getContext(executionId);
    if (!context) {
      throw new Error(`Workflow execution ${executionId} not found`);
    }

    context.status = 'cancelled';
    context.endTime = new Date();
    this.contextManager.setContext(executionId, context);
    
    this.logger.info('Workflow cancelled', { executionId });
  }

  async getWorkflowStatus(executionId: string): Promise<WorkflowExecutionContext> {
    const context = this.contextManager.getContext(executionId);
    if (!context) {
      throw new Error(`Workflow execution ${executionId} not found`);
    }
    return context;
  }

  private generateExecutionId(): string {
    return `exec-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private buildExecutionGraph(definition: WorkflowDefinition): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    
    // Build dependency graph
    for (const node of definition.nodes) {
      graph.set(node.id, node.dependencies);
    }
    
    return graph;
  }
}
