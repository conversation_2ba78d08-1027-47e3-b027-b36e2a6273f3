import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { registerLangbaseEndpoint } from './langbase';
import { persistenceRoutes } from './persistence-routes';
import { transformRoutes } from './transform';
import { emailRoutes } from './email-service';
import vendorDiscoveryRoutes from './vendor-discovery';
import { agentResultsRoutes } from './agent-results-routes';

// Create Hono app
const app = new Hono();

// Middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', cors());
app.use('*', secureHeaders());

// Routes
app.get('/', c => {
	// Helpful redirect in development to the UI server
	const isDev = process.env.NODE_ENV === 'development';
	if (isDev) {
		return c.redirect('http://localhost:3100', 302);
	}
	return c.json({
		message: 'Agent App Server',
		version: '1.0.0'
	});
});

// Register agent endpoint
registerLangbaseEndpoint(app);

// Register persistence JSON-backed routes
app.route('/', persistenceRoutes);

// Register transform routes for Phase 2 structuring
app.route('/', transformRoutes);

// Register email routes
app.route('/', emailRoutes);

// Register vendor discovery routes
app.route('/', vendorDiscoveryRoutes);

// Register agent results routes (PostgreSQL with pgvector)
app.route('/', agentResultsRoutes);

// 404 handler
app.notFound(c => {
	return c.json(
		{
			status: 404,
			message: 'Not Found'
		},
		404
	);
});

// Error handler
app.onError((err, c) => {
	console.error(`${err}`);

	const isDev = process.env.NODE_ENV === 'development';

	return c.json(
		{
			status: 500,
			message: 'Internal Server Error',
			...(isDev && { error: err.message, stack: err.stack })
		},
		500
	);
});

export { app };
