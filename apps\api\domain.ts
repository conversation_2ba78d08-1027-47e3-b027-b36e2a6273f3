import { z } from 'zod';

// Duplicate minimal domain types for server-side (to avoid relying on frontend alias paths)
// Keep in sync with src/store/rfqStore.ts and src/types/p2p.ts where relevant

export const UUID = z.string().min(1);

export const VendorSchema = z.object({
  id: UUID,
  name: z.string(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  company: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url().optional(),
  favorite: z.boolean().optional(),
  notes: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});
export type Vendor = z.infer<typeof VendorSchema>;

export const AttachmentSchema = z.object({
  id: UUID,
  name: z.string(),
  url: z.string().url().optional(),
  type: z.string().optional(),
});

export const PartyInfoSchema = z.object({
  company: z.string(),
  contactName: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
});

export const RFQLineItemSchema = z.object({
  id: UUID,
  itemName: z.string(),
  partNumber: z.string().optional(),
  quantity: z.number(),
  unit: z.string().optional(),
  materialSpec: z.string().optional(),
  dueDate: z.string().optional(),
  notes: z.string().optional(),
  imageUrl: z.string().url().optional(),
});

export const RFQStatusSchema = z.enum(['Draft', 'Sent', 'Responded', 'Awarded', 'Closed']);

export const HistoryEntrySchema = z.object({
  id: UUID,
  rfqId: UUID,
  at: z.string(),
  user: z.string().optional(),
  action: z.enum(['Created','Modified','Sent','Responded','Awarded','Closed','AI_Generated','RevisionRequested','Rejected']),
  details: z.string().optional(),
});

export const RFQSchema = z.object({
  id: UUID,
  refNumber: z.string(),
  title: z.string(),
  description: z.string().optional(),
  status: RFQStatusSchema,
  client: PartyInfoSchema,
  vendor: PartyInfoSchema.optional(),
  createdAt: z.string(),
  dueDate: z.string().optional(),
  responseDeadline: z.string().optional(),
  notes: z.string().optional(),
  attachments: z.array(AttachmentSchema),
  lineItems: z.array(RFQLineItemSchema),
  invitedVendorIds: z.array(UUID),
  history: z.array(HistoryEntrySchema),
});
export type RFQ = z.infer<typeof RFQSchema>;

export const ItemPriceQuoteSchema = z.object({
  lineItemId: UUID,
  unitPrice: z.number(),
  currency: z.string(),
  notes: z.string().optional(),
});

export const VendorQuoteSchema = z.object({
  id: UUID,
  rfqId: UUID,
  vendorId: UUID,
  items: z.array(ItemPriceQuoteSchema),
  total: z.number(),
  currency: z.string(),
  deliveryTerms: z.string().optional(),
  deliveryDate: z.string().optional(),
  attachments: z.array(AttachmentSchema).optional(),
  notes: z.string().optional(),
  status: z.enum(['Submitted','RevisionRequested','Rejected','Awarded']),
  submittedAt: z.string(),
});
export type VendorQuote = z.infer<typeof VendorQuoteSchema>;

export const GeneratedReportSchema = z.object({
  id: UUID,
  rfqId: UUID,
  title: z.string(),
  sections: z.array(z.object({ heading: z.string(), markdown: z.string().optional(), html: z.string().optional() })),
  createdAt: z.string(),
});
export type GeneratedReport = z.infer<typeof GeneratedReportSchema>;

export const NotificationSchema = z.object({
  id: UUID,
  type: z.enum(['info','success','warning','error']),
  title: z.string(),
  message: z.string().optional(),
  createdAt: z.string(),
  read: z.boolean().optional(),
});

export const AppStateSchema = z.object({
  vendors: z.array(VendorSchema),
  rfqs: z.array(RFQSchema),
  quotes: z.array(VendorQuoteSchema),
  reports: z.array(GeneratedReportSchema),
});
export type AppState = z.infer<typeof AppStateSchema>;

// Structured AI payloads for Phase 2
export const AIOutputPayloadSchema = z.object({
  type: z.enum(['rfq','report','proposal']),
  rfq: z.any().optional(),
  report: z.any().optional(),
  proposal: z.any().optional(),
  context: z.string().optional(),
});
export type AIOutputPayload = z.infer<typeof AIOutputPayloadSchema>;

