import { useMemo, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { Invoice, PurchaseOrder, GoodsReceiptNote, UUID } from "@/types/p2p";

export type InvoiceMatchingProps = {
  invoices: Invoice[];
  pos: PurchaseOrder[];
  grns: GoodsReceiptNote[];
  onSubmitInvoice: (invoice: Invoice) => void;
  onStatusUpdate: (id: UUID, status: Invoice["status"]) => void;
};

export function InvoiceMatching({ invoices, pos, grns, onSubmitInvoice, onStatusUpdate }: InvoiceMatchingProps) {
  const [form, setForm] = useState<{ vendorId: string; poId?: string; totalAmount: number; currency: string }>(() => ({ vendorId: "", poId: "", totalAmount: 0, currency: "INR" }));

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!form.vendorId) return;
    onSubmitInvoice({ id: crypto.randomUUID(), vendorId: form.vendorId, poId: form.poId || undefined, lines: [], status: "submitted", totalAmount: form.totalAmount, currency: form.currency });
    setForm({ vendorId: "", poId: "", totalAmount: 0, currency: "INR" });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Invoice Matching (2/3-way)</CardTitle>
        <CardDescription>Submit vendor invoices and track matching status</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-6 gap-2">
          <Input placeholder="Vendor ID" value={form.vendorId} onChange={e => setForm({ ...form, vendorId: e.target.value })} />
          <Input placeholder="PO ID (optional)" value={form.poId} onChange={e => setForm({ ...form, poId: e.target.value })} />
          <Input type="number" step="0.01" placeholder="Total Amount" value={form.totalAmount} onChange={e => setForm({ ...form, totalAmount: Number(e.target.value) })} />
          <Input placeholder="Currency" value={form.currency} onChange={e => setForm({ ...form, currency: e.target.value })} />
          <Button type="submit">Submit Invoice</Button>
        </form>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {invoices.map(inv => (
            <div className="border rounded-md p-3" key={inv.id}>
              <div className="flex items-center justify-between">
                <div className="font-medium">Invoice #{inv.id.slice(0, 8)}</div>
                <select value={inv.status} onChange={e => onStatusUpdate(inv.id, e.target.value as any)} className="border rounded-md px-2 py-1 bg-transparent">
                  <option value="draft">Draft</option>
                  <option value="submitted">Submitted</option>
                  <option value="matched">Matched</option>
                  <option value="exception">Exception</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
              <div className="text-xs text-muted-foreground">Vendor: {inv.vendorId} • Amount: {inv.totalAmount} {inv.currency}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

