import { Hono } from 'hono';
import { requirePermission } from '../middleware/auth';
import { diContainer, TOKENS, generateUUID } from '@procuregpt/shared';
import type { JobQueue, BulkRFQJobData } from '@procuregpt/shared';

const rfq = new Hono();

rfq.get('/', async (c) => {
  // TODO: Implement RFQ listing
  return c.json({
    success: true,
    data: {
      items: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
    },
    timestamp: new Date(),
  });
});

rfq.post('/', requirePermission('rfq:create'), async (c) => {
  // TODO: Implement RFQ creation
  return c.json({
    success: true,
    data: { id: 'rfq-123', status: 'created' },
    message: 'RFQ created',
    timestamp: new Date(),
  }, 201);
});

rfq.post('/:id/send', requirePermission('rfq:send'), async (c) => {
  const rfqId = c.req.param('id');
  const body = await c.req.json();
  const { supplierIds, templateId = 'default_rfq_template', sendDelay = 1000 } = body;

  if (!supplierIds || !Array.isArray(supplierIds) || supplierIds.length === 0) {
    return c.json({
      success: false,
      error: 'supplierIds array is required and must not be empty',
      timestamp: new Date(),
    }, 400);
  }

  // Get job queue from DI container
  const jobQueue = diContainer.resolve<JobQueue>(TOKENS.JOB_QUEUE);

  // Create bulk RFQ job data
  const jobData: BulkRFQJobData = {
    id: generateUUID(),
    type: 'send_bulk_rfq',
    rfqId,
    supplierIds,
    templateId,
    attachments: body.attachments || [],
    sendDelay,
    priority: 'high', // RFQ sending is high priority
  };

  // Queue the bulk RFQ job
  const job = await jobQueue.add(jobData, {
    priority: 'high',
  });

  return c.json({
    success: true,
    data: {
      jobId: job.id,
      rfqId,
      status: 'queued',
      recipientCount: supplierIds.length
    },
    message: 'Bulk RFQ send queued for processing',
    timestamp: new Date(),
  }, 202);
});

export { rfq as rfqRoutes };
