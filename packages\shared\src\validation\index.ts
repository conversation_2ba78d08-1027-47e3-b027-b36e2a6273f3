import { z } from 'zod';
import { ValidationError } from '../errors';

/**
 * Validation utilities using Zod
 */

/**
 * Validate data against a Zod schema
 */
export function validate<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      const field = firstError.path.join('.');
      throw new ValidationError(firstError.message, field);
    }
    throw error;
  }
}

/**
 * Safely validate data and return result with success flag
 */
export function safeValidate<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  error?: string;
} {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return { 
        success: false, 
        error: `${firstError.path.join('.')}: ${firstError.message}` 
      };
    }
    return { success: false, error: String(error) };
  }
}

/**
 * Create a middleware function for validating request bodies
 */
export function validateBody<T>(schema: z.ZodSchema<T>) {
  return (req: any, res: any, next: any) => {
    try {
      req.body = validate(schema, req.body);
      next();
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          code: error.code,
        });
      } else {
        next(error);
      }
    }
  };
}

/**
 * Create a middleware function for validating query parameters
 */
export function validateQuery<T>(schema: z.ZodSchema<T>) {
  return (req: any, res: any, next: any) => {
    try {
      req.query = validate(schema, req.query);
      next();
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          code: error.code,
        });
      } else {
        next(error);
      }
    }
  };
}

/**
 * Create a middleware function for validating route parameters
 */
export function validateParams<T>(schema: z.ZodSchema<T>) {
  return (req: any, res: any, next: any) => {
    try {
      req.params = validate(schema, req.params);
      next();
    } catch (error) {
      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          code: error.code,
        });
      } else {
        next(error);
      }
    }
  };
}

/**
 * Common validation schemas
 */
export const CommonSchemas = {
  id: z.string().uuid(),
  email: z.string().email(),
  password: z.string().min(8),
  url: z.string().url(),
  positiveNumber: z.number().positive(),
  nonEmptyString: z.string().min(1),
  pagination: z.object({
    page: z.number().positive().default(1),
    limit: z.number().positive().max(100).default(20),
  }),
};
