import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import type { RFQLineItem } from "@/store/rfqStore";

export function LineItemRow({ item, onChange, onRemove }: { item: RFQLineItem; onChange: (patch: Partial<RFQLineItem>) => void; onRemove: () => void }) {
  return (
    <tr className="align-top">
      <td className="p-2"><Input value={item.itemName} onChange={e => onChange({ itemName: e.target.value })} placeholder="Item/Description" /></td>
      <td className="p-2"><Input value={item.partNumber || ""} onChange={e => onChange({ partNumber: e.target.value })} placeholder="Part #" /></td>
      <td className="p-2 w-24"><Input type="number" value={item.quantity} onChange={e => onChange({ quantity: Number(e.target.value) })} /></td>
      <td className="p-2 w-28"><Input value={item.unit || ""} onChange={e => onChange({ unit: e.target.value })} placeholder="Unit" /></td>
      <td className="p-2"><Input value={item.materialSpec || ""} onChange={e => onChange({ materialSpec: e.target.value })} placeholder="Material/Spec" /></td>
      <td className="p-2 w-40"><Input type="date" value={item.dueDate?.slice(0,10) || ""} onChange={e => onChange({ dueDate: e.target.value ? new Date(e.target.value).toISOString() : undefined })} /></td>
      <td className="p-2"><Input value={item.notes || ""} onChange={e => onChange({ notes: e.target.value })} placeholder="Notes" /></td>
      <td className="p-2 w-28 text-right"><Button variant="ghost" size="sm" onClick={onRemove}>Remove</Button></td>
    </tr>
  );
}

