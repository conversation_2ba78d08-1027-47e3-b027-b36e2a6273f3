import { <PERSON>o } from 'hono';
import { validate, diContainer, TOKENS } from '@procuregpt/shared';
import { requirePermission } from '../middleware/auth';
import { WorkflowExecutionRequestSchema } from '@procuregpt/types';
import type { WorkflowExecutionRequest, JobResponse, JWTPayload } from '@procuregpt/types';
import type { JobQueue, WorkflowExecutionJobData } from '@procuregpt/shared';
import { generateUUID } from '@procuregpt/shared';

const workflow = new Hono();

// Execute a workflow (async)
workflow.post('/execute', requirePermission('workflow:execute'), async (c) => {
  const body = await c.req.json();
  const request = validate(WorkflowExecutionRequestSchema, body);
  const user = c.get('user') as JWTPayload;

  // Get job queue from DI container
  const jobQueue = diContainer.resolve<JobQueue>(TOKENS.JOB_QUEUE);

  // Create workflow execution job data
  const executionId = generateUUID();
  const jobData: WorkflowExecutionJobData = {
    id: generateUUID(),
    type: 'workflow_execution',
    workflowId: request.workflowId,
    input: request.input,
    variables: request.variables,
    userId: user.sub,
    executionId,
    priority: request.priority || 'normal',
    timeout: request.timeout,
  };

  // Queue the workflow for execution
  const job = await jobQueue.add(jobData, {
    priority: request.priority || 'normal',
    timeout: request.timeout,
  });

  const jobResponse: JobResponse = {
    jobId: job.id!,
    status: 'queued',
    createdAt: new Date(),
    estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
  };

  return c.json({
    success: true,
    data: jobResponse,
    message: 'Workflow queued for execution',
    timestamp: new Date(),
  }, 202);
});

// Get workflow execution status
workflow.get('/:id/status', async (c) => {
  const jobId = c.req.param('id');

  // Get job queue from DI container
  const jobQueue = diContainer.resolve<JobQueue>(TOKENS.JOB_QUEUE);

  // Get job status and progress
  const [status, progress, job] = await Promise.all([
    jobQueue.getJobStatus(jobId),
    jobQueue.getJobProgress(jobId),
    jobQueue.getJob(jobId)
  ]);

  if (!job) {
    return c.json({
      success: false,
      error: 'Job not found',
      timestamp: new Date(),
    }, 404);
  }

  const jobResponse: JobResponse = {
    jobId,
    status: status as any || 'unknown',
    progress: progress?.percentage,
    createdAt: new Date(job.timestamp),
    startedAt: job.processedOn ? new Date(job.processedOn) : undefined,
    completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
  };

  return c.json({
    success: true,
    data: jobResponse,
    timestamp: new Date(),
  });
});

// Get workflow execution result
workflow.get('/:id/result', async (c) => {
  const jobId = c.req.param('id');

  // Get job queue from DI container
  const jobQueue = diContainer.resolve<JobQueue>(TOKENS.JOB_QUEUE);

  // Get job result
  const result = await jobQueue.getJobResult(jobId);

  if (!result) {
    return c.json({
      success: false,
      error: 'Job result not found or job not completed',
      timestamp: new Date(),
    }, 404);
  }

  return c.json({
    success: true,
    data: {
      jobId,
      ...result,
    },
    timestamp: new Date(),
  });
});

// List workflow executions
workflow.get('/', async (c) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  
  // TODO: Get actual executions from database
  const mockExecutions = [
    {
      jobId: 'job-1',
      status: 'completed',
      createdAt: new Date(Date.now() - 60 * 60 * 1000),
      completedAt: new Date(Date.now() - 55 * 60 * 1000),
    },
    {
      jobId: 'job-2',
      status: 'running',
      progress: 45,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      startedAt: new Date(Date.now() - 8 * 60 * 1000),
    },
  ];

  return c.json({
    success: true,
    data: {
      items: mockExecutions,
      pagination: {
        page,
        limit,
        total: 2,
        totalPages: 1,
      },
    },
    timestamp: new Date(),
  });
});

export { workflow as workflowRoutes };
