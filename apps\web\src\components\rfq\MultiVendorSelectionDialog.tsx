import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Mail, Phone, Users, Send } from "lucide-react";
import { VendorSearchComponent } from "./VendorSearchComponent";
import { useRFQStore } from "@/store/rfqStore";
import type { Vendor } from "@/types/p2p";

interface MultiVendorSelectionDialogProps {
  onSendToVendors?: (vendors: Vendor[]) => void;
  triggerText?: string;
  triggerVariant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  disabled?: boolean;
}

export function MultiVendorSelectionDialog({
  onSendToVendors,
  triggerText = "Select Vendors",
  triggerVariant = "outline",
  disabled = false
}: MultiVendorSelectionDialogProps) {
  const [open, setOpen] = useState(false);
  const { vendorSearch, clearSelectedVendors } = useRFQStore();

  const handleSendToSelected = () => {
    if (vendorSearch.selectedVendors.length > 0) {
      onSendToVendors?.(vendorSearch.selectedVendors);
      setOpen(false);
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const getVendorEmails = () => {
    return vendorSearch.selectedVendors
      .filter(vendor => vendor.email)
      .map(vendor => vendor.email!)
      .filter(Boolean);
  };

  const getVendorPhones = () => {
    const phones: string[] = [];
    vendorSearch.selectedVendors.forEach(vendor => {
      if (vendor.phone) phones.push(vendor.phone);
      if (vendor.mobile) phones.push(vendor.mobile);
    });
    return phones;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={triggerVariant} disabled={disabled}>
          <Users className="h-4 w-4 mr-2" />
          {triggerText}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Select Vendors for RFQ
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[70vh]">
          {/* Vendor Search Section */}
          <div className="lg:col-span-2">
            <VendorSearchComponent
              multiSelect={true}
              showSelected={false}
              className="h-full"
            />
          </div>

          {/* Selected Vendors Summary */}
          <div className="space-y-4">
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  Selected Vendors ({vendorSearch.selectedVendors.length})
                  {vendorSearch.selectedVendors.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearSelectedVendors}
                    >
                      Clear All
                    </Button>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 pt-0">
                {vendorSearch.selectedVendors.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No vendors selected</p>
                    <p className="text-xs">Search and select vendors to send RFQ</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <ScrollArea className="h-[300px]">
                      <div className="space-y-3">
                        {vendorSearch.selectedVendors.map((vendor) => (
                          <SelectedVendorCard key={vendor.id} vendor={vendor} />
                        ))}
                      </div>
                    </ScrollArea>

                    <Separator />

                    {/* Contact Summary */}
                    <div className="space-y-3">
                      <div className="text-sm font-medium">Contact Summary</div>
                      
                      {getVendorEmails().length > 0 && (
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Mail className="h-3 w-3" />
                            Email Addresses ({getVendorEmails().length})
                          </div>
                          <div className="text-xs bg-muted p-2 rounded max-h-20 overflow-y-auto">
                            {getVendorEmails().join(', ')}
                          </div>
                        </div>
                      )}

                      {getVendorPhones().length > 0 && (
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Phone className="h-3 w-3" />
                            Phone Numbers ({getVendorPhones().length})
                          </div>
                          <div className="text-xs bg-muted p-2 rounded max-h-20 overflow-y-auto">
                            {getVendorPhones().join(', ')}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {vendorSearch.selectedVendors.length > 0 && (
              <>
                {vendorSearch.selectedVendors.length} vendor{vendorSearch.selectedVendors.length !== 1 ? 's' : ''} selected
                {getVendorEmails().length > 0 && (
                  <> • {getVendorEmails().length} email{getVendorEmails().length !== 1 ? 's' : ''}</>
                )}
              </>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSendToSelected}
              disabled={vendorSearch.selectedVendors.length === 0 || getVendorEmails().length === 0}
            >
              <Send className="h-4 w-4 mr-2" />
              Send RFQ to Selected
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface SelectedVendorCardProps {
  vendor: Vendor;
}

function SelectedVendorCard({ vendor }: SelectedVendorCardProps) {
  const { unselectVendor } = useRFQStore();

  return (
    <div className="border rounded-lg p-3 space-y-2">
      <div className="flex items-center justify-between">
        <div className="font-medium text-sm">{vendor.name}</div>
        <button
          onClick={() => unselectVendor(vendor.id)}
          className="text-muted-foreground hover:text-foreground text-xs"
        >
          Remove
        </button>
      </div>
      
      {vendor.company && (
        <div className="text-xs text-muted-foreground">{vendor.company}</div>
      )}

      <div className="space-y-1">
        {vendor.email && (
          <div className="flex items-center gap-1 text-xs">
            <Mail className="h-3 w-3 text-muted-foreground" />
            <span className="truncate">{vendor.email}</span>
          </div>
        )}
        
        {vendor.phone && (
          <div className="flex items-center gap-1 text-xs">
            <Phone className="h-3 w-3 text-muted-foreground" />
            <span>{vendor.phone}</span>
          </div>
        )}
        
        {vendor.mobile && (
          <div className="flex items-center gap-1 text-xs">
            <Phone className="h-3 w-3 text-muted-foreground" />
            <Badge variant="outline" className="text-xs px-1 py-0">Mobile</Badge>
            <span>{vendor.mobile}</span>
          </div>
        )}
      </div>

      {!vendor.email && (
        <div className="text-xs text-orange-600 bg-orange-50 p-1 rounded">
          ⚠️ No email address
        </div>
      )}
    </div>
  );
}
