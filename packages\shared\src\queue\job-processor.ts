import { Worker, Job, WorkerOptions } from 'bullmq';
import Redis from 'ioredis';
import { Service, Singleton, Inject, TOKENS } from '../di';
import type { Logger } from '../logger';
import type { JobData, JobResult, JobProgress } from './job-types';

export interface JobProcessor {
  start(): Promise<void>;
  stop(): Promise<void>;
  isRunning(): boolean;
  registerHandler<T extends JobData>(
    jobType: string, 
    handler: (job: Job<T>) => Promise<JobResult>
  ): void;
  updateProgress(job: Job, progress: JobProgress): Promise<void>;
}

@Singleton()
@Service
export class BullMQJobProcessor implements JobProcessor {
  private worker: Worker | null = null;
  private redis: Redis;
  private handlers = new Map<string, (job: Job<any>) => Promise<JobResult>>();
  private isWorkerRunning = false;

  constructor(
    @Inject(TOKENS.CONFIG) private config: { redisUrl?: string; queueName: string },
    @Inject(TOKENS.LOGGER) private logger: Logger
  ) {
    // Initialize Redis connection
    this.redis = new Redis(config.redisUrl || 'redis://localhost:6379', {
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      lazyConnect: true,
    });

    this.logger.info('BullMQ job processor initialized', {
      queueName: config.queueName
    });
  }

  async start(): Promise<void> {
    if (this.isWorkerRunning) {
      this.logger.warn('Job processor is already running');
      return;
    }

    const workerOptions: WorkerOptions = {
      connection: this.redis,
      concurrency: 5, // Process up to 5 jobs concurrently
      maxStalledCount: 1,
      stalledInterval: 30000,
      removeOnComplete: 100,
      removeOnFail: 50,
    };

    this.worker = new Worker(
      this.config.queueName,
      this.processJob.bind(this),
      workerOptions
    );

    this.setupWorkerEventListeners();

    this.isWorkerRunning = true;
    this.logger.info('Job processor started', {
      queueName: this.config.queueName,
      concurrency: workerOptions.concurrency
    });
  }

  async stop(): Promise<void> {
    if (!this.worker || !this.isWorkerRunning) {
      this.logger.warn('Job processor is not running');
      return;
    }

    await this.worker.close();
    await this.redis.quit();
    
    this.worker = null;
    this.isWorkerRunning = false;
    
    this.logger.info('Job processor stopped');
  }

  isRunning(): boolean {
    return this.isWorkerRunning;
  }

  registerHandler<T extends JobData>(
    jobType: string,
    handler: (job: Job<T>) => Promise<JobResult>
  ): void {
    this.handlers.set(jobType, handler);
    this.logger.info('Job handler registered', { jobType });
  }

  async updateProgress(job: Job, progress: JobProgress): Promise<void> {
    try {
      await job.updateProgress(progress);
      this.logger.debug('Job progress updated', {
        jobId: job.id,
        percentage: progress.percentage,
        message: progress.message
      });
    } catch (error) {
      this.logger.error('Failed to update job progress', error as Error, {
        jobId: job.id
      });
    }
  }

  private async processJob(job: Job<JobData>): Promise<JobResult> {
    const startTime = Date.now();
    
    this.logger.info('Processing job', {
      jobId: job.id,
      jobType: job.name,
      attempts: job.attemptsMade + 1,
      maxAttempts: job.opts.attempts
    });

    try {
      // Update progress to indicate job has started
      await this.updateProgress(job, {
        percentage: 0,
        message: 'Job started',
        timestamp: new Date(),
      });

      // Get handler for this job type
      const handler = this.handlers.get(job.name);
      if (!handler) {
        throw new Error(`No handler registered for job type: ${job.name}`);
      }

      // Execute the job
      const result = await handler(job);
      
      // Update progress to 100%
      await this.updateProgress(job, {
        percentage: 100,
        message: 'Job completed',
        timestamp: new Date(),
      });

      const executionTime = Date.now() - startTime;
      
      this.logger.info('Job processed successfully', {
        jobId: job.id,
        jobType: job.name,
        executionTime,
        success: result.success
      });

      return {
        ...result,
        executionTime,
        completedAt: new Date(),
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = (error as Error).message;

      this.logger.error('Job processing failed', error as Error, {
        jobId: job.id,
        jobType: job.name,
        executionTime,
        attempts: job.attemptsMade + 1
      });

      // Update progress to indicate failure
      await this.updateProgress(job, {
        percentage: 0,
        message: `Job failed: ${errorMessage}`,
        timestamp: new Date(),
      });

      return {
        success: false,
        error: errorMessage,
        executionTime,
        completedAt: new Date(),
        metadata: {
          attempts: job.attemptsMade + 1,
          maxAttempts: job.opts.attempts,
        },
      };
    }
  }

  private setupWorkerEventListeners(): void {
    if (!this.worker) return;

    this.worker.on('completed', (job) => {
      this.logger.info('Worker completed job', {
        jobId: job.id,
        jobType: job.name,
        duration: job.processedOn ? job.processedOn - job.timestamp : 0
      });
    });

    this.worker.on('failed', (job, err) => {
      this.logger.error('Worker failed to process job', err, {
        jobId: job?.id,
        jobType: job?.name,
        attempts: job?.attemptsMade,
        maxAttempts: job?.opts.attempts
      });
    });

    this.worker.on('stalled', (jobId) => {
      this.logger.warn('Worker job stalled', { jobId });
    });

    this.worker.on('error', (error) => {
      this.logger.error('Worker error', error);
    });

    this.worker.on('ready', () => {
      this.logger.info('Worker is ready to process jobs');
    });

    this.worker.on('closing', () => {
      this.logger.info('Worker is closing');
    });
  }
}
