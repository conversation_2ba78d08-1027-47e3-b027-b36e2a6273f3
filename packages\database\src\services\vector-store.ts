import { <PERSON>, Singleton, Inject, TOKENS } from '@procuregpt/shared';
import type { VectorEmbedding, SemanticSearchQuery, SemanticSearchResult } from '@procuregpt/types';
import type { DatabaseClient } from '../client';
import type { Logger } from '@procuregpt/shared';

export interface VectorStore {
  insertEmbedding(embedding: Omit<VectorEmbedding, 'id' | 'createdAt'>): Promise<VectorEmbedding>;
  searchSimilar(query: SemanticSearchQuery): Promise<SemanticSearchResult[]>;
  deleteEmbedding(id: string): Promise<void>;
  updateEmbedding(id: string, updates: Partial<VectorEmbedding>): Promise<VectorEmbedding>;
}

@Singleton()
@Service
export class PgVectorStore implements VectorStore {
  constructor(
    @Inject(TOKENS.DATABASE_CLIENT) private db: DatabaseClient,
    @Inject(TOKENS.LOGGER) private logger: Logger
  ) {}

  async insertEmbedding(embedding: Omit<VectorEmbedding, 'id' | 'createdAt'>): Promise<VectorEmbedding> {
    const query = `
      INSERT INTO vector_embeddings (content, embedding, metadata, source)
      VALUES ($1, $2, $3, $4)
      RETURNING id, content, embedding, metadata, source, created_at
    `;
    
    const params = [
      embedding.content,
      JSON.stringify(embedding.embedding),
      JSON.stringify(embedding.metadata),
      embedding.source,
    ];

    const [result] = await this.db.query<any>(query, params);
    
    return {
      id: result.id,
      content: result.content,
      embedding: JSON.parse(result.embedding),
      metadata: result.metadata,
      source: result.source,
      createdAt: result.created_at,
    };
  }

  async searchSimilar(searchQuery: SemanticSearchQuery): Promise<SemanticSearchResult[]> {
    // Convert query to embedding first (this would typically be done by an embedding service)
    // For now, we'll assume the query is already an embedding vector
    const queryEmbedding = await this.getQueryEmbedding(searchQuery.query);
    
    const query = `
      SELECT 
        id,
        content,
        metadata,
        source,
        1 - (embedding <=> $1::vector) as similarity_score
      FROM vector_embeddings
      WHERE 1 - (embedding <=> $1::vector) > $2
      ORDER BY embedding <=> $1::vector
      LIMIT $3
    `;

    const params = [
      JSON.stringify(queryEmbedding),
      searchQuery.threshold,
      searchQuery.limit,
    ];

    const results = await this.db.query<any>(query, params);
    
    return results.map(row => ({
      id: row.id,
      content: row.content,
      score: row.similarity_score,
      metadata: row.metadata,
      source: row.source,
    }));
  }

  async deleteEmbedding(id: string): Promise<void> {
    const query = 'DELETE FROM vector_embeddings WHERE id = $1';
    await this.db.query(query, [id]);
  }

  async updateEmbedding(id: string, updates: Partial<VectorEmbedding>): Promise<VectorEmbedding> {
    const setClause = [];
    const params = [];
    let paramIndex = 1;

    if (updates.content !== undefined) {
      setClause.push(`content = $${paramIndex++}`);
      params.push(updates.content);
    }

    if (updates.embedding !== undefined) {
      setClause.push(`embedding = $${paramIndex++}`);
      params.push(JSON.stringify(updates.embedding));
    }

    if (updates.metadata !== undefined) {
      setClause.push(`metadata = $${paramIndex++}`);
      params.push(JSON.stringify(updates.metadata));
    }

    if (updates.source !== undefined) {
      setClause.push(`source = $${paramIndex++}`);
      params.push(updates.source);
    }

    params.push(id);

    const query = `
      UPDATE vector_embeddings 
      SET ${setClause.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, content, embedding, metadata, source, created_at
    `;

    const [result] = await this.db.query<any>(query, params);
    
    return {
      id: result.id,
      content: result.content,
      embedding: JSON.parse(result.embedding),
      metadata: result.metadata,
      source: result.source,
      createdAt: result.created_at,
    };
  }

  private async getQueryEmbedding(query: string): Promise<number[]> {
    // TODO: This should use the embedding service to convert text to vector
    // For now, return a mock embedding
    this.logger.debug('Getting embedding for query', { query: query.substring(0, 50) });
    
    // Mock 1536-dimensional embedding (OpenAI text-embedding-ada-002 size)
    return new Array(1536).fill(0).map(() => Math.random() - 0.5);
  }
}
