import { z } from 'zod';
import { DatabaseConfigSchema, EmailConfigSchema } from '@procuregpt/types';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Application Configuration Schema
const AppConfigSchema = z.object({
  // Server Configuration
  port: z.number().default(3101),
  host: z.string().default('localhost'),
  nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
  
  // API Configuration
  apiPrefix: z.string().default('/api'),
  corsOrigins: z.array(z.string()).default(['http://localhost:3000']),
  
  // JWT Configuration
  jwtSecret: z.string(),
  jwtExpiresIn: z.string().default('24h'),
  refreshTokenExpiresIn: z.string().default('7d'),
  
  // Database Configuration
  database: DatabaseConfigSchema,
  
  // Email Configuration
  email: EmailConfigSchema,
  
  // External APIs
  perplexityApiKey: z.string(),
  openaiApiKey: z.string().optional(),
  
  // File Upload
  uploadDir: z.string().default('./uploads'),
  maxFileSize: z.number().default(10 * 1024 * 1024), // 10MB
  
  // Workflow Engine
  workflowTimeout: z.number().default(300000), // 5 minutes
  maxConcurrentWorkflows: z.number().default(10),
  
  // Job Queue
  redisUrl: z.string().optional(),
  queueName: z.string().default('procuregpt-jobs'),
  
  // Logging
  logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  logFormat: z.enum(['json', 'pretty']).default('pretty'),
});

export type AppConfig = z.infer<typeof AppConfigSchema>;

// Load and validate configuration
export function loadConfig(): AppConfig {
  const config = {
    port: Number(process.env.PORT) || 3101,
    host: process.env.HOST || 'localhost',
    nodeEnv: process.env.NODE_ENV as 'development' | 'production' | 'test' || 'development',
    
    apiPrefix: process.env.API_PREFIX || '/api',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    
    jwtSecret: process.env.JWT_SECRET || '',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    
    database: {
      host: process.env.DATABASE_HOST || 'localhost',
      port: Number(process.env.DATABASE_PORT) || 5432,
      database: process.env.DATABASE_NAME || 'procuregpt',
      user: process.env.DATABASE_USER || 'postgres',
      password: process.env.DATABASE_PASSWORD || '',
      ssl: process.env.DATABASE_SSL === 'true',
      poolMin: Number(process.env.DATABASE_POOL_MIN) || 2,
      poolMax: Number(process.env.DATABASE_POOL_MAX) || 10,
      connectionString: process.env.DATABASE_URL,
    },
    
    email: {
      host: process.env.EMAIL_HOST || '',
      port: Number(process.env.EMAIL_PORT) || 587,
      secure: process.env.EMAIL_SECURE === 'true',
      user: process.env.EMAIL_USER || '',
      password: process.env.EMAIL_PASSWORD || '',
      from: process.env.EMAIL_FROM || '',
    },
    
    perplexityApiKey: process.env.PERPLEXITY_API_KEY || '',
    openaiApiKey: process.env.OPENAI_API_KEY,
    
    uploadDir: process.env.UPLOAD_DIR || './uploads',
    maxFileSize: Number(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024,
    
    workflowTimeout: Number(process.env.WORKFLOW_TIMEOUT) || 300000,
    maxConcurrentWorkflows: Number(process.env.MAX_CONCURRENT_WORKFLOWS) || 10,
    
    redisUrl: process.env.REDIS_URL,
    queueName: process.env.QUEUE_NAME || 'procuregpt-jobs',
    
    logLevel: (process.env.LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug') || 'info',
    logFormat: (process.env.LOG_FORMAT as 'json' | 'pretty') || 'pretty',
  };

  return AppConfigSchema.parse(config);
}

// Export singleton instance
export const appConfig = loadConfig();
