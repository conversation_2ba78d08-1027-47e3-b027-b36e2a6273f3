id: "procurement-agent-v2"
name: "Enhanced Procurement Agent Workflow"
description: "Intelligent procurement workflow with RAG, HITL, and multi-vendor RFQ capabilities"
version: "2.0.0"

variables:
  region: "Mangaluru, India"
  max_budget: 50000
  esg_required: true

nodes:
  - id: "query_analysis"
    type: "call_llm"
    name: "Analyze Procurement Query"
    description: "Analyze user query to determine procurement intent and requirements"
    config:
      prompt_template: "procurement_query_analysis"
      model: "perplexity"
      max_tokens: 1000
    dependencies: []
    timeout: 30000

  - id: "context_retrieval"
    type: "semantic_search"
    name: "Retrieve Relevant Context"
    description: "Search for relevant procurement policies, past contracts, and supplier data"
    config:
      query_source: "${nodeResults.query_analysis.result.search_terms}"
      limit: 10
      threshold: 0.7
      include_categories: ["policies", "contracts", "suppliers"]
    dependencies: ["query_analysis"]
    timeout: 15000

  - id: "market_research"
    type: "call_llm"
    name: "Conduct Market Research"
    description: "Research current market conditions, pricing, and supplier availability"
    config:
      prompt_template: "market_research"
      model: "perplexity"
      max_tokens: 2000
      context: "${nodeResults.context_retrieval.result}"
    dependencies: ["query_analysis", "context_retrieval"]
    timeout: 60000

  - id: "supplier_recommendations"
    type: "call_llm"
    name: "Generate Supplier Recommendations"
    description: "Analyze market data and generate supplier recommendations"
    config:
      prompt_template: "supplier_recommendations"
      model: "perplexity"
      max_tokens: 1500
      context: "${nodeResults.market_research.result}"
      variables:
        region: "${variables.region}"
        esg_required: "${variables.esg_required}"
    dependencies: ["market_research"]
    timeout: 45000

  - id: "human_approval"
    type: "human_approval"
    name: "Review Supplier Recommendations"
    description: "Human review and approval of recommended suppliers"
    config:
      title: "Supplier Recommendation Review"
      description: "Please review the recommended suppliers and approve those you want to include in the RFQ"
      data: "${nodeResults.supplier_recommendations.result}"
      required_approvers: ["procurement_manager"]
      timeout_hours: 24
    dependencies: ["supplier_recommendations"]

  - id: "rfq_generation"
    type: "call_llm"
    name: "Generate RFQ Document"
    description: "Create detailed RFQ document based on approved suppliers"
    config:
      prompt_template: "rfq_generation"
      model: "perplexity"
      max_tokens: 3000
      context: "${nodeResults.human_approval.result.approved_suppliers}"
      variables:
        budget_range: "${variables.max_budget}"
    dependencies: ["human_approval"]
    condition: "${nodeResults.human_approval.result.status === 'approved'}"
    timeout: 60000

  - id: "vendor_contact_enrichment"
    type: "data_transform"
    name: "Enrich Vendor Contact Information"
    description: "Retrieve and validate vendor email addresses and contact details"
    config:
      source_data: "${nodeResults.human_approval.result.approved_suppliers}"
      enrichment_fields: ["email", "phone", "contact_person"]
      validation: true
    dependencies: ["human_approval"]
    condition: "${nodeResults.human_approval.result.status === 'approved'}"
    timeout: 30000

  - id: "send_rfq_emails"
    type: "send_email"
    name: "Send RFQ to Selected Vendors"
    description: "Send the generated RFQ to all approved vendors via email"
    config:
      template_id: "rfq_email_template"
      recipients: "${nodeResults.vendor_contact_enrichment.result.contacts}"
      subject: "Request for Quotation - ${nodeResults.query_analysis.result.category}"
      attachments:
        - content: "${nodeResults.rfq_generation.result.document}"
          filename: "RFQ_${Date.now()}.pdf"
          type: "application/pdf"
      priority: "high"
    dependencies: ["rfq_generation", "vendor_contact_enrichment"]
    timeout: 30000

  - id: "workflow_summary"
    type: "data_transform"
    name: "Generate Workflow Summary"
    description: "Create a comprehensive summary of the procurement workflow execution"
    config:
      template: "workflow_summary"
      data:
        query_analysis: "${nodeResults.query_analysis.result}"
        market_research: "${nodeResults.market_research.result}"
        approved_suppliers: "${nodeResults.human_approval.result.approved_suppliers}"
        rfq_document: "${nodeResults.rfq_generation.result}"
        email_status: "${nodeResults.send_rfq_emails.result}"
    dependencies: ["send_rfq_emails"]
    timeout: 10000

metadata:
  category: "procurement"
  tags: ["rfq", "suppliers", "market-research", "hitl"]
  created_by: "system"
  created_at: "2025-01-18T00:00:00Z"
  version_notes: "Enhanced workflow with RAG integration and human-in-the-loop approval"
