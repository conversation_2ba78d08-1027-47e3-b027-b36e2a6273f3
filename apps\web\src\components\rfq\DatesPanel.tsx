import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

export function DatesPanel({ createdAt, dueDate, responseDeadline, onChange }: { createdAt: string; dueDate?: string; responseDeadline?: string; onChange: (patch: Partial<{ createdAt: string; dueDate?: string; responseDeadline?: string }>) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Dates</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="text-sm">Created</label>
          <Input type="date" value={createdAt.slice(0,10)} onChange={e => onChange({ createdAt: new Date(e.target.value).toISOString() })} />
        </div>
        <div>
          <label className="text-sm">Due</label>
          <Input type="date" value={dueDate?.slice(0,10) || ""} onChange={e => onChange({ dueDate: e.target.value ? new Date(e.target.value).toISOString() : undefined })} />
        </div>
        <div>
          <label className="text-sm">Response Deadline</label>
          <Input type="date" value={responseDeadline?.slice(0,10) || ""} onChange={e => onChange({ responseDeadline: e.target.value ? new Date(e.target.value).toISOString() : undefined })} />
        </div>
      </CardContent>
    </Card>
  );
}

