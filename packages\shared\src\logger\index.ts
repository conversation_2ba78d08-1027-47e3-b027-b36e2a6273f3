import winston from 'winston';

export interface Logger {
  error(message: string, error?: Error, meta?: any): void;
  warn(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  debug(message: string, meta?: any): void;
}

export function createLogger(level: string = 'info', format: 'json' | 'pretty' = 'pretty'): Logger {
  const logFormat = format === 'json' 
    ? winston.format.json()
    : winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
          return `${timestamp} [${level}]: ${message}${metaStr}`;
        })
      );

  const logger = winston.createLogger({
    level,
    format: logFormat,
    transports: [
      new winston.transports.Console(),
      new winston.transports.File({ 
        filename: 'logs/error.log', 
        level: 'error',
        handleExceptions: true 
      }),
      new winston.transports.File({ 
        filename: 'logs/combined.log',
        handleExceptions: true 
      }),
    ],
    exitOnError: false,
  });

  return {
    error: (message: string, error?: Error, meta?: any) => {
      logger.error(message, { error: error?.message, stack: error?.stack, ...meta });
    },
    warn: (message: string, meta?: any) => {
      logger.warn(message, meta);
    },
    info: (message: string, meta?: any) => {
      logger.info(message, meta);
    },
    debug: (message: string, meta?: any) => {
      logger.debug(message, meta);
    },
  };
}
