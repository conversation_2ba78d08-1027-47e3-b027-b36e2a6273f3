import { z } from 'zod';

// Email Configuration
export const EmailConfigSchema = z.object({
  host: z.string(),
  port: z.number().positive(),
  secure: z.boolean().default(false),
  user: z.string(),
  password: z.string(),
  from: z.string().email(),
});

export type EmailConfig = z.infer<typeof EmailConfigSchema>;

// Email Recipient
export const EmailRecipientSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
});

export type EmailRecipient = z.infer<typeof EmailRecipientSchema>;

// Email Attachment
export const EmailAttachmentSchema = z.object({
  filename: z.string(),
  content: z.string(), // base64 encoded
  contentType: z.string(),
  size: z.number().positive().optional(),
});

export type EmailAttachment = z.infer<typeof EmailAttachmentSchema>;

// Email Template Variables
export const EmailTemplateVariablesSchema = z.record(z.any());

export type EmailTemplateVariables = z.infer<typeof EmailTemplateVariablesSchema>;

// Email Message
export const EmailMessageSchema = z.object({
  to: z.array(EmailRecipientSchema),
  cc: z.array(EmailRecipientSchema).default([]),
  bcc: z.array(EmailRecipientSchema).default([]),
  subject: z.string(),
  text: z.string().optional(),
  html: z.string().optional(),
  attachments: z.array(EmailAttachmentSchema).default([]),
  replyTo: z.string().email().optional(),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  templateId: z.string().optional(),
  templateVariables: EmailTemplateVariablesSchema.default({}),
});

export type EmailMessage = z.infer<typeof EmailMessageSchema>;

// Email Template
export const EmailTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  subject: z.string(),
  textContent: z.string().optional(),
  htmlContent: z.string(),
  variables: z.array(z.string()).default([]),
  category: z.string().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type EmailTemplate = z.infer<typeof EmailTemplateSchema>;

// Email Send Result
export const EmailSendResultSchema = z.object({
  messageId: z.string(),
  accepted: z.array(z.string()),
  rejected: z.array(z.string()),
  pending: z.array(z.string()),
  response: z.string(),
  envelope: z.object({
    from: z.string(),
    to: z.array(z.string()),
  }),
});

export type EmailSendResult = z.infer<typeof EmailSendResultSchema>;

// Bulk Email Request
export const BulkEmailRequestSchema = z.object({
  templateId: z.string(),
  recipients: z.array(z.object({
    email: z.string().email(),
    name: z.string().optional(),
    variables: EmailTemplateVariablesSchema.default({}),
  })),
  subject: z.string().optional(),
  attachments: z.array(EmailAttachmentSchema).default([]),
  sendAt: z.date().optional(),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
});

export type BulkEmailRequest = z.infer<typeof BulkEmailRequestSchema>;

// Email Queue Job
export const EmailQueueJobSchema = z.object({
  id: z.string(),
  type: z.enum(['single', 'bulk', 'template']),
  message: EmailMessageSchema.optional(),
  bulkRequest: BulkEmailRequestSchema.optional(),
  status: z.enum(['queued', 'processing', 'sent', 'failed']),
  attempts: z.number().default(0),
  maxAttempts: z.number().default(3),
  error: z.string().optional(),
  scheduledAt: z.date().optional(),
  processedAt: z.date().optional(),
  createdAt: z.date(),
});

export type EmailQueueJob = z.infer<typeof EmailQueueJobSchema>;
