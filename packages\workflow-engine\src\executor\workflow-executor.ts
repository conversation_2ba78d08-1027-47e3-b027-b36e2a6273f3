import { Service, Singleton, Inject, TOKENS } from '@procuregpt/shared';
import type { 
  WorkflowDefinition, 
  WorkflowExecutionContext, 
  WorkflowNode,
  NodeExecutionResult,
  NodeExecutionStatus
} from '@procuregpt/types';
import type { Logger } from '@procuregpt/shared';
import { NodeExecutorFactory } from '../nodes';

export interface WorkflowExecutor {
  execute(
    definition: WorkflowDefinition,
    context: WorkflowExecutionContext,
    executionGraph: Map<string, string[]>
  ): Promise<WorkflowExecutionContext>;
}

@Singleton()
@Service
export class GraphWorkflowExecutor implements WorkflowExecutor {
  constructor(
    @Inject(TOKENS.LOGGER) private logger: Logger,
    private nodeExecutorFactory: NodeExecutorFactory = new NodeExecutorFactory()
  ) {}

  async execute(
    definition: WorkflowDefinition,
    context: WorkflowExecutionContext,
    executionGraph: Map<string, string[]>
  ): Promise<WorkflowExecutionContext> {
    const nodeMap = new Map(definition.nodes.map(node => [node.id, node]));
    const executedNodes = new Set<string>();
    const runningNodes = new Set<string>();
    
    // Find nodes with no dependencies (starting nodes)
    const readyNodes = definition.nodes.filter(node => 
      node.dependencies.length === 0
    );

    this.logger.info('Starting workflow execution', {
      totalNodes: definition.nodes.length,
      readyNodes: readyNodes.length
    });

    // Execute nodes in dependency order
    while (executedNodes.size < definition.nodes.length) {
      // Find nodes that are ready to execute
      const nodesToExecute = definition.nodes.filter(node => 
        !executedNodes.has(node.id) && 
        !runningNodes.has(node.id) &&
        node.dependencies.every(dep => executedNodes.has(dep))
      );

      if (nodesToExecute.length === 0) {
        // Check if we have running nodes
        if (runningNodes.size === 0) {
          // No nodes running and no nodes ready - possible circular dependency
          const remainingNodes = definition.nodes.filter(node => !executedNodes.has(node.id));
          throw new Error(`Circular dependency detected or missing dependencies. Remaining nodes: ${remainingNodes.map(n => n.id).join(', ')}`);
        }
        
        // Wait for running nodes to complete
        await this.sleep(100);
        continue;
      }

      // Execute ready nodes (potentially in parallel)
      const nodePromises = nodesToExecute.map(async (node) => {
        runningNodes.add(node.id);
        
        try {
          const result = await this.executeNode(node, context);
          context.nodeResults[node.id] = result;
          executedNodes.add(node.id);
          runningNodes.delete(node.id);
          
          this.logger.debug('Node executed successfully', {
            nodeId: node.id,
            status: result.status,
            executionTime: result.executionTime
          });
          
          return result;
        } catch (error) {
          runningNodes.delete(node.id);
          
          this.logger.error('Node execution failed', error as Error, {
            nodeId: node.id,
            nodeType: node.type
          });
          
          // Create failed result
          const failedResult: NodeExecutionResult = {
            nodeId: node.id,
            status: 'failed',
            error: (error as Error).message,
            startTime: new Date(),
            endTime: new Date(),
            executionTime: 0,
          };
          
          context.nodeResults[node.id] = failedResult;
          executedNodes.add(node.id);
          
          // Decide whether to continue or fail the entire workflow
          if (this.shouldFailWorkflow(node, error as Error)) {
            context.status = 'failed';
            context.error = `Node ${node.id} failed: ${(error as Error).message}`;
            throw error;
          }
          
          return failedResult;
        }
      });

      // Wait for all nodes in this batch to complete
      await Promise.all(nodePromises);
    }

    // All nodes completed successfully
    context.status = 'completed';
    context.endTime = new Date();
    
    this.logger.info('Workflow execution completed successfully', {
      executionId: context.executionId,
      totalNodes: definition.nodes.length,
      successfulNodes: Object.values(context.nodeResults).filter(r => r.status === 'completed').length,
      failedNodes: Object.values(context.nodeResults).filter(r => r.status === 'failed').length
    });

    return context;
  }

  private async executeNode(node: WorkflowNode, context: WorkflowExecutionContext): Promise<NodeExecutionResult> {
    const startTime = new Date();
    
    this.logger.debug('Executing node', {
      nodeId: node.id,
      nodeType: node.type,
      dependencies: node.dependencies
    });

    // Check if node should be skipped based on condition
    if (node.condition && !this.evaluateCondition(node.condition, context)) {
      return {
        nodeId: node.id,
        status: 'skipped',
        startTime,
        endTime: new Date(),
        executionTime: 0,
        metadata: { reason: 'condition_not_met' }
      };
    }

    // Get node executor
    const executor = this.nodeExecutorFactory.getExecutor(node.type);
    
    // Execute node with timeout
    const timeoutMs = node.timeout || 300000; // 5 minutes default
    const executionPromise = executor.execute(node, context);
    
    let result: any;
    try {
      result = await Promise.race([
        executionPromise,
        this.createTimeoutPromise(timeoutMs, node.id)
      ]);
    } catch (error) {
      if ((error as Error).message.includes('timeout')) {
        throw new Error(`Node ${node.id} timed out after ${timeoutMs}ms`);
      }
      throw error;
    }

    const endTime = new Date();
    const executionTime = endTime.getTime() - startTime.getTime();

    return {
      nodeId: node.id,
      status: 'completed',
      result,
      startTime,
      endTime,
      executionTime,
    };
  }

  private evaluateCondition(condition: string, context: WorkflowExecutionContext): boolean {
    try {
      // Simple condition evaluation - in production, use a proper expression evaluator
      // For now, just check if it's a simple boolean or variable reference
      if (condition === 'true') return true;
      if (condition === 'false') return false;
      
      // Check variable references like ${variables.someFlag}
      const variableMatch = condition.match(/\$\{variables\.(\w+)\}/);
      if (variableMatch) {
        const variableName = variableMatch[1];
        return Boolean(context.variables[variableName]);
      }
      
      // Default to true if we can't evaluate
      return true;
    } catch (error) {
      this.logger.warn('Failed to evaluate condition, defaulting to true', {
        condition,
        error: (error as Error).message
      });
      return true;
    }
  }

  private shouldFailWorkflow(node: WorkflowNode, error: Error): boolean {
    // For now, fail the workflow on any node failure
    // In the future, this could be configurable per node
    return true;
  }

  private createTimeoutPromise(timeoutMs: number, nodeId: string): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Node ${nodeId} execution timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
