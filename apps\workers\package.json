{"name": "@procuregpt/workers", "version": "1.0.0", "description": "Background job workers for ProcureGPT", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "devDependencies": {"typescript": "^5.3.3", "tsx": "^4.7.0", "vitest": "^1.2.0", "@types/node": "^20.11.0"}, "dependencies": {"@procuregpt/types": "workspace:*", "@procuregpt/config": "workspace:*", "@procuregpt/shared": "workspace:*", "@procuregpt/database": "workspace:*", "@procuregpt/workflow-engine": "workspace:*", "@procuregpt/email-service": "workspace:*", "tsyringe": "^4.8.0", "reflect-metadata": "^0.2.1", "bullmq": "^5.1.0", "ioredis": "^5.3.2"}}