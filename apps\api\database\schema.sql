-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Agent execution results table
CREATE TABLE IF NOT EXISTS agent_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id VARCHAR(255) NOT NULL UNIQUE,
    
    -- Input and execution metadata
    input_text TEXT NOT NULL,
    input_hash VARCHAR(64) NOT NULL, -- SHA-256 hash of input for deduplication
    
    -- Execution status and timing
    status VARCHAR(50) NOT NULL CHECK (status IN ('running', 'completed', 'failed', 'terminated')),
    started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    execution_time_ms INTEGER,
    
    -- Agent workflow results (JSON fields)
    query_analysis JSONB,
    market_research JSONB,
    recommendations JSONB,
    documents JSONB,
    action_plan JSONB,
    rfq_data JSONB,
    git_commit_info JSONB,
    
    -- Error handling
    error_message TEXT,
    error_trace JSONB,
    partial_results BOOLEAN DEFAULT FALSE,
    
    -- Workflow control metadata
    workflow_config JSON<PERSON>,
    workflow_steps JSONB,
    total_steps INTEGER,
    completed_steps INTEGER,
    failed_steps INTEGER,
    
    -- System metadata
    system_status JSONB,
    region VARCHAR(255) DEFAULT 'Mangaluru, India',
    agent_version VARCHAR(50),
    
    -- Embeddings for semantic search (1536 dimensions for OpenAI text-embedding-ada-002)
    input_embedding vector(1536),
    output_embedding vector(1536),
    combined_embedding vector(1536),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Agent execution steps table (for detailed step tracking)
CREATE TABLE IF NOT EXISTS agent_execution_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    result_id UUID NOT NULL REFERENCES agent_results(id) ON DELETE CASCADE,
    workflow_id VARCHAR(255) NOT NULL,
    
    -- Step identification
    step_id VARCHAR(255) NOT NULL,
    step_name VARCHAR(500) NOT NULL,
    step_order INTEGER NOT NULL,
    
    -- Step execution details
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'timeout')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,
    attempts INTEGER DEFAULT 1,
    
    -- Step results and errors
    result_data JSONB,
    error_message TEXT,
    error_details JSONB,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure unique step per result
    UNIQUE(result_id, step_id)
);

-- Agent result tags table (for categorization and filtering)
CREATE TABLE IF NOT EXISTS agent_result_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    result_id UUID NOT NULL REFERENCES agent_results(id) ON DELETE CASCADE,
    tag_name VARCHAR(100) NOT NULL,
    tag_value VARCHAR(500),
    tag_type VARCHAR(50) DEFAULT 'custom' CHECK (tag_type IN ('system', 'user', 'custom', 'auto')),
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure unique tag per result
    UNIQUE(result_id, tag_name)
);

-- Indexes for performance

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_agent_results_workflow_id ON agent_results(workflow_id);
CREATE INDEX IF NOT EXISTS idx_agent_results_status ON agent_results(status);
CREATE INDEX IF NOT EXISTS idx_agent_results_started_at ON agent_results(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_results_input_hash ON agent_results(input_hash);

-- Vector similarity search indexes (using HNSW for better performance)
CREATE INDEX IF NOT EXISTS idx_agent_results_input_embedding ON agent_results 
    USING hnsw (input_embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
    
CREATE INDEX IF NOT EXISTS idx_agent_results_output_embedding ON agent_results 
    USING hnsw (output_embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
    
CREATE INDEX IF NOT EXISTS idx_agent_results_combined_embedding ON agent_results 
    USING hnsw (combined_embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_agent_results_status_started_at ON agent_results(status, started_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_results_region_started_at ON agent_results(region, started_at DESC);

-- Step tracking indexes
CREATE INDEX IF NOT EXISTS idx_agent_execution_steps_result_id ON agent_execution_steps(result_id);
CREATE INDEX IF NOT EXISTS idx_agent_execution_steps_workflow_id ON agent_execution_steps(workflow_id);
CREATE INDEX IF NOT EXISTS idx_agent_execution_steps_status ON agent_execution_steps(status);

-- Tag indexes
CREATE INDEX IF NOT EXISTS idx_agent_result_tags_result_id ON agent_result_tags(result_id);
CREATE INDEX IF NOT EXISTS idx_agent_result_tags_tag_name ON agent_result_tags(tag_name);
CREATE INDEX IF NOT EXISTS idx_agent_result_tags_tag_type ON agent_result_tags(tag_type);

-- JSON indexes for common queries
CREATE INDEX IF NOT EXISTS idx_agent_results_query_analysis ON agent_results USING GIN (query_analysis);
CREATE INDEX IF NOT EXISTS idx_agent_results_recommendations ON agent_results USING GIN (recommendations);
CREATE INDEX IF NOT EXISTS idx_agent_results_rfq_data ON agent_results USING GIN (rfq_data);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_agent_results_updated_at 
    BEFORE UPDATE ON agent_results 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_execution_steps_updated_at 
    BEFORE UPDATE ON agent_execution_steps 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
