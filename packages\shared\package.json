{"name": "@procuregpt/shared", "version": "1.0.0", "description": "Shared utilities and services for ProcureGPT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "devDependencies": {"typescript": "^5.3.3", "vitest": "^1.2.0", "@types/node": "^20.11.0"}, "dependencies": {"@procuregpt/types": "workspace:*", "@procuregpt/config": "workspace:*", "tsyringe": "^4.8.0", "reflect-metadata": "^0.2.1", "winston": "^3.11.0", "zod": "^3.25.67", "bullmq": "^5.1.0", "ioredis": "^5.3.2"}}