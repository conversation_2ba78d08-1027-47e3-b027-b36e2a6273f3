import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Calendar, Mail, Phone, Building, MapPin, User, FileText, Package } from "lucide-react";
import { RFQEmailModal } from "./RFQEmailModal";
import { MultiVendorSelectionDialog } from "./MultiVendorSelectionDialog";
import { useRFQStore } from "@/store/rfqStore";
import type { RFQ, Vendor } from "@/store/rfqStore";

interface StructuredRFQDisplayProps {
  rfq: RFQ;
  className?: string;
}

export function StructuredRFQDisplay({ rfq, className = "" }: StructuredRFQDisplayProps) {
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [selectedVendors, setSelectedVendors] = useState<Vendor[]>([]);
  const { vendors, sendRFQEmail } = useRFQStore();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleSendToVendors = (vendors: Vendor[]) => {
    setSelectedVendors(vendors);
    setShowEmailModal(true);
  };

  const handleSendEmail = async (emailData: any) => {
    await sendRFQEmail(emailData);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="bg-card/50 border-2">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-6 w-6 text-primary" />
              <CardTitle className="text-xl">Request for Quotation</CardTitle>
            </div>
            <Badge variant="outline" className="text-sm">
              Draft
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* RFQ Details and Client Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* RFQ Details */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg text-muted-foreground">RFQ Details</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">RFQ No.:</span>
                    <span className="ml-2 font-semibold">{rfq.refNumber}</span>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Date of Issue:</span>
                    <span className="ml-2">{formatDate(rfq.createdAt)}</span>
                  </div>
                </div>
                
                {rfq.responseDeadline && (
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Response Deadline:</span>
                      <span className="ml-2">{formatDate(rfq.responseDeadline)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Client Information */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg text-muted-foreground">Client Information</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <span className="font-semibold">{rfq.client.company}</span>
                  </div>
                </div>
                
                {rfq.client.contactName && (
                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Contact:</span>
                      <span className="ml-2">{rfq.client.contactName}</span>
                    </div>
                  </div>
                )}
                
                {rfq.client.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{rfq.client.email}</span>
                  </div>
                )}
                
                {rfq.client.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{rfq.client.phone}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* RFQ Title and Description */}
          <div className="space-y-4">
            <h2 className="text-2xl font-bold">{rfq.title}</h2>
            {rfq.description && (
              <div className="prose prose-sm max-w-none">
                <p className="text-muted-foreground leading-relaxed">{rfq.description}</p>
              </div>
            )}
          </div>

          <Separator />

          {/* Product Specifications */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Package className="h-5 w-5" />
              Product Specifications
            </h3>
            
            {rfq.lineItems.length > 0 ? (
              <div className="space-y-3">
                {rfq.lineItems.map((item, index) => (
                  <Card key={item.id} className="bg-muted/30">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="font-medium">Item #{index + 1}: {item.itemName}</div>
                          {item.partNumber && (
                            <div className="text-sm text-muted-foreground">Part #: {item.partNumber}</div>
                          )}
                          <div className="text-sm">
                            Quantity: {item.quantity} {item.unit || 'units'}
                          </div>
                          {item.materialSpec && (
                            <div className="text-sm text-muted-foreground">Spec: {item.materialSpec}</div>
                          )}
                        </div>
                        {item.notes && (
                          <div className="text-xs text-muted-foreground max-w-xs">
                            {item.notes}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No product specifications added yet</p>
              </div>
            )}
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={() => setShowEmailModal(true)}
              className="flex-1 flex items-center justify-center gap-2"
            >
              <Mail className="h-4 w-4" />
              Send RFQ via Email
            </Button>
            
            <MultiVendorSelectionDialog
              onSendToVendors={handleSendToVendors}
              triggerText="Send to Multiple Vendors"
              triggerVariant="outline"
            />
          </div>
        </CardContent>
      </Card>

      {/* Email Modal */}
      <RFQEmailModal
        isOpen={showEmailModal}
        onClose={() => setShowEmailModal(false)}
        rfq={rfq}
        vendors={vendors}
        onSendEmail={handleSendEmail}
        preSelectedVendors={selectedVendors}
      />
    </div>
  );
}
