import { <PERSON>o } from 'hono';
import { diContainer, TOKENS } from '@procuregpt/shared';
import type { Logger } from '@procuregpt/shared';

const health = new Hono();

health.get('/', async (c) => {
  const logger = diContainer.resolve<Logger>(TOKENS.LOGGER);
  
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      api: 'healthy',
      database: 'checking...',
      queue: 'checking...',
      email: 'checking...',
    },
  };

  try {
    // TODO: Add actual health checks for services
    // const dbService = diContainer.resolve(TOKENS.DATABASE_CLIENT);
    // await dbService.ping();
    healthCheck.services.database = 'healthy';
    
    // const queueService = diContainer.resolve(TOKENS.JOB_QUEUE);
    // await queueService.ping();
    healthCheck.services.queue = 'healthy';
    
    // const emailService = diContainer.resolve(TOKENS.EMAIL_SERVICE);
    // await emailService.ping();
    healthCheck.services.email = 'healthy';

    logger.debug('Health check completed', healthCheck);
    
    return c.json(healthCheck);
  } catch (error) {
    logger.error('Health check failed', error as Error);
    
    return c.json({
      ...healthCheck,
      status: 'unhealthy',
      error: (error as Error).message,
    }, 503);
  }
});

health.get('/ready', async (c) => {
  // Readiness probe - check if all dependencies are ready
  try {
    // TODO: Add readiness checks
    return c.json({
      status: 'ready',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return c.json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: (error as Error).message,
    }, 503);
  }
});

health.get('/live', async (c) => {
  // Liveness probe - basic check if service is alive
  return c.json({
    status: 'alive',
    timestamp: new Date().toISOString(),
  });
});

export { health as healthRoutes };
