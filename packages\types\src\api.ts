import { z } from 'zod';

// API Response Wrapper
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  timestamp: z.date(),
  requestId: z.string().optional(),
});

export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
  requestId?: string;
};

// Pagination
export const PaginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(20),
  total: z.number().nonnegative().optional(),
  totalPages: z.number().nonnegative().optional(),
});

export type Pagination = z.infer<typeof PaginationSchema>;

// Paginated Response
export const PaginatedResponseSchema = z.object({
  items: z.array(z.any()),
  pagination: PaginationSchema,
});

export type PaginatedResponse<T = any> = {
  items: T[];
  pagination: Pagination;
};

// Job Status
export const JobStatusSchema = z.enum([
  'queued',
  'running',
  'completed',
  'failed',
  'cancelled'
]);

export type JobStatus = z.infer<typeof JobStatusSchema>;

// Job Response
export const JobResponseSchema = z.object({
  jobId: z.string(),
  status: JobStatusSchema,
  progress: z.number().min(0).max(100).optional(),
  result: z.any().optional(),
  error: z.string().optional(),
  createdAt: z.date(),
  startedAt: z.date().optional(),
  completedAt: z.date().optional(),
  estimatedCompletion: z.date().optional(),
});

export type JobResponse = z.infer<typeof JobResponseSchema>;

// Workflow Execution Request
export const WorkflowExecutionRequestSchema = z.object({
  workflowId: z.string(),
  input: z.record(z.any()),
  variables: z.record(z.any()).default({}),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  timeout: z.number().positive().optional(),
});

export type WorkflowExecutionRequest = z.infer<typeof WorkflowExecutionRequestSchema>;

// File Upload
export const FileUploadSchema = z.object({
  id: z.string(),
  filename: z.string(),
  originalName: z.string(),
  mimeType: z.string(),
  size: z.number().positive(),
  path: z.string(),
  uploadedBy: z.string().optional(),
  uploadedAt: z.date(),
  metadata: z.record(z.any()).default({}),
});

export type FileUpload = z.infer<typeof FileUploadSchema>;
