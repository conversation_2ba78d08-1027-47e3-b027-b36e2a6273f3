import { Service, Singleton, Inject, TOKENS } from '@procuregpt/shared';
import type { SemanticSearchQuery, SemanticSearchResult } from '@procuregpt/types';
import type { VectorStore } from './vector-store';
import type { EmbeddingService } from './embedding-service';
import type { Logger } from '@procuregpt/shared';

export interface SemanticSearchService {
  search(query: SemanticSearchQuery): Promise<SemanticSearchResult[]>;
  indexDocument(content: string, metadata?: Record<string, any>, source?: string): Promise<string>;
  removeDocument(id: string): Promise<void>;
}

@Singleton()
@Service
export class RAGSemanticSearchService implements SemanticSearchService {
  constructor(
    @Inject(TOKENS.VECTOR_STORE) private vectorStore: VectorStore,
    @Inject(TOKENS.EMBEDDING_SERVICE) private embeddingService: EmbeddingService,
    @Inject(TOKENS.LOGGER) private logger: Logger
  ) {}

  async search(query: SemanticSearchQuery): Promise<SemanticSearchResult[]> {
    this.logger.info('Performing semantic search', { 
      query: query.query.substring(0, 100),
      limit: query.limit,
      threshold: query.threshold 
    });

    try {
      // Generate embedding for the search query
      const queryEmbedding = await this.embeddingService.generateEmbedding(query.query);
      
      // Create a modified query with the embedding
      const vectorQuery: SemanticSearchQuery = {
        ...query,
        query: JSON.stringify(queryEmbedding), // Pass embedding as string for now
      };

      // Search for similar vectors
      const results = await this.vectorStore.searchSimilar(vectorQuery);
      
      this.logger.info('Semantic search completed', { 
        resultCount: results.length,
        topScore: results[0]?.score || 0 
      });

      return results;
    } catch (error) {
      this.logger.error('Semantic search failed', error as Error, { query: query.query.substring(0, 100) });
      throw error;
    }
  }

  async indexDocument(content: string, metadata: Record<string, any> = {}, source?: string): Promise<string> {
    this.logger.info('Indexing document', { 
      contentLength: content.length,
      source,
      metadataKeys: Object.keys(metadata) 
    });

    try {
      // Generate embedding for the content
      const embedding = await this.embeddingService.generateEmbedding(content);
      
      // Store in vector store
      const result = await this.vectorStore.insertEmbedding({
        content,
        embedding,
        metadata,
        source,
      });

      this.logger.info('Document indexed successfully', { 
        id: result.id,
        contentLength: content.length 
      });

      return result.id;
    } catch (error) {
      this.logger.error('Failed to index document', error as Error, { 
        contentLength: content.length,
        source 
      });
      throw error;
    }
  }

  async removeDocument(id: string): Promise<void> {
    this.logger.info('Removing document from index', { id });

    try {
      await this.vectorStore.deleteEmbedding(id);
      this.logger.info('Document removed from index', { id });
    } catch (error) {
      this.logger.error('Failed to remove document from index', error as Error, { id });
      throw error;
    }
  }
}
