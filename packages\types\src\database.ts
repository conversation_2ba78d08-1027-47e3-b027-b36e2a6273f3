import { z } from 'zod';

// Database Connection Configuration
export const DatabaseConfigSchema = z.object({
  host: z.string(),
  port: z.number().positive(),
  database: z.string(),
  user: z.string(),
  password: z.string(),
  ssl: z.boolean().default(false),
  poolMin: z.number().positive().default(2),
  poolMax: z.number().positive().default(10),
  connectionString: z.string().optional(),
});

export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;

// Vector Embedding
export const VectorEmbeddingSchema = z.object({
  id: z.string(),
  content: z.string(),
  embedding: z.array(z.number()),
  metadata: z.record(z.any()).default({}),
  source: z.string().optional(),
  createdAt: z.date(),
});

export type VectorEmbedding = z.infer<typeof VectorEmbeddingSchema>;

// Document for RAG
export const DocumentSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  type: z.enum(['pdf', 'docx', 'txt', 'md', 'html']),
  source: z.string().optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
  createdAt: z.date(),
  updatedAt: z.date(),
  isProcessed: z.boolean().default(false),
  chunkCount: z.number().default(0),
});

export type Document = z.infer<typeof DocumentSchema>;

// Document Chunk for RAG
export const DocumentChunkSchema = z.object({
  id: z.string(),
  documentId: z.string(),
  content: z.string(),
  chunkIndex: z.number(),
  embedding: z.array(z.number()).optional(),
  metadata: z.record(z.any()).default({}),
  createdAt: z.date(),
});

export type DocumentChunk = z.infer<typeof DocumentChunkSchema>;

// Semantic Search Query
export const SemanticSearchQuerySchema = z.object({
  query: z.string(),
  limit: z.number().positive().default(10),
  threshold: z.number().min(0).max(1).default(0.7),
  filters: z.record(z.any()).default({}),
  includeMetadata: z.boolean().default(true),
});

export type SemanticSearchQuery = z.infer<typeof SemanticSearchQuerySchema>;

// Semantic Search Result
export const SemanticSearchResultSchema = z.object({
  id: z.string(),
  content: z.string(),
  score: z.number().min(0).max(1),
  metadata: z.record(z.any()).default({}),
  source: z.string().optional(),
  documentId: z.string().optional(),
  chunkIndex: z.number().optional(),
});

export type SemanticSearchResult = z.infer<typeof SemanticSearchResultSchema>;
