import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { StatusBadge } from "./StatusBadge";
import { ClientInfoPanel } from "./ClientInfoPanel";
import { DatesPanel } from "./DatesPanel";
import { AttachmentPanel } from "./AttachmentPanel";
import { LineItemsTable } from "./LineItemsTable";
import { ActionButton } from "./ActionButton";
import { useRFQStore } from "@/store/rfqStore";
import { TimelinePanel } from "./TimelinePanel";
import { VendorSelectionDialog } from "./VendorSelectionDialog";
import { ProposalComparisonTable } from "./ProposalComparisonTable";
import { ReportPreviewModal } from "./ReportPreviewModal";
import { VendorEditorDialog } from "./VendorEditorDialog";

export function RFQDetailCard({ id }: { id: string }) {
  const rfq = useRFQStore(s => s.rfqs.find(r => r.id === id));
  const updateRFQ = useRFQStore(s => s.updateRFQ);
  const addLineItem = useRFQStore(s => s.addLineItem);
  const updateLineItem = useRFQStore(s => s.updateLineItem);
  const removeLineItem = useRFQStore(s => s.removeLineItem);
  const addAttachment = useRFQStore(s => s.addAttachment);
  const removeAttachment = useRFQStore(s => s.removeAttachment);
  const sendRFQ = useRFQStore(s => s.sendRFQ);

  if (!rfq) return null;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-3">
            {rfq.title}
            <StatusBadge status={rfq.status} />
          </CardTitle>
          <CardDescription>Ref: {rfq.refNumber}</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <VendorSelectionDialog vendors={useRFQStore.getState().vendors} selected={rfq.invitedVendorIds} onChange={(ids) => updateRFQ(rfq.id, { invitedVendorIds: ids })} />
          <VendorEditorDialog />
          <ActionButton onClick={() => sendRFQ(rfq.id)}>Send</ActionButton>
          <ReportPreviewModal rfqId={rfq.id} />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm">RFQ Title</label>
            <Input value={rfq.title} onChange={e => updateRFQ(rfq.id, { title: e.target.value })} />
          </div>
          <div>
            <label className="text-sm">Ref #</label>
            <Input value={rfq.refNumber} onChange={e => updateRFQ(rfq.id, { refNumber: e.target.value })} />
          </div>
        </div>

        <div>
          <label className="text-sm">Description</label>
          <Textarea value={rfq.description || ""} onChange={e => updateRFQ(rfq.id, { description: e.target.value })} />
        </div>

        <Separator />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ClientInfoPanel value={rfq.client} onChange={(v) => updateRFQ(rfq.id, { client: v })} />
          <DatesPanel createdAt={rfq.createdAt} dueDate={rfq.dueDate} responseDeadline={rfq.responseDeadline} onChange={(patch) => updateRFQ(rfq.id, patch)} />
        </div>

        <Separator />
        <div>
          <h3 className="text-lg font-semibold mb-2">Line Items</h3>
          <LineItemsTable
            items={rfq.lineItems}
            onChange={(lineId, patch) => updateLineItem(rfq.id, lineId, patch)}
            onAdd={() => addLineItem(rfq.id, { itemName: "", quantity: 1 })}
            onRemove={(lineId) => removeLineItem(rfq.id, lineId)}
          />
        </div>

        <Separator />
        <AttachmentPanel items={rfq.attachments} onAdd={(f) => addAttachment(rfq.id, f)} onRemove={(attId) => removeAttachment(rfq.id, attId)} />

        <Separator />
        <ProposalComparisonTable rfqId={rfq.id} />

        <Separator />
        <TimelinePanel rfqId={rfq.id} />
      </CardContent>
    </Card>
  );
}

