import { container, DependencyContainer } from 'tsyringe';
import { TOKENS } from './tokens';
import { AppConfig } from '@procuregpt/config';

/**
 * Dependency Injection Container
 * 
 * This class provides a centralized way to configure and manage
 * all dependencies in the ProcureGPT application.
 */
export class DIContainer {
  private static instance: DIContainer;
  private container: DependencyContainer;

  private constructor() {
    this.container = container.createChildContainer();
  }

  public static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer();
    }
    return DIContainer.instance;
  }

  /**
   * Get the underlying tsyringe container
   */
  public getContainer(): DependencyContainer {
    return this.container;
  }

  /**
   * Register a service with the container
   */
  public register<T>(token: symbol, implementation: new (...args: any[]) => T): void {
    this.container.register(token, { useClass: implementation });
  }

  /**
   * Register a singleton service with the container
   */
  public registerSingleton<T>(token: symbol, implementation: new (...args: any[]) => T): void {
    this.container.registerSingleton(token, implementation);
  }

  /**
   * Register an instance with the container
   */
  public registerInstance<T>(token: symbol, instance: T): void {
    this.container.registerInstance(token, instance);
  }

  /**
   * Resolve a service from the container
   */
  public resolve<T>(token: symbol): T {
    return this.container.resolve<T>(token);
  }

  /**
   * Check if a service is registered
   */
  public isRegistered(token: symbol): boolean {
    return this.container.isRegistered(token);
  }

  /**
   * Clear all registrations (useful for testing)
   */
  public clear(): void {
    this.container.clearInstances();
  }

  /**
   * Initialize core services
   * This method should be called during application startup
   */
  public async initializeCoreServices(config: AppConfig): Promise<void> {
    // Register configuration
    this.registerInstance(TOKENS.CONFIG, config);

    // Register logger
    const { createLogger } = await import('../logger');
    const logger = createLogger(config.logLevel, config.logFormat);
    this.registerInstance(TOKENS.LOGGER, logger);

    console.log('✅ Core services initialized in DI container');
  }

  /**
   * Initialize database services
   */
  public async initializeDatabaseServices(): Promise<void> {
    // These will be implemented when we create the database package
    console.log('📦 Database services registration placeholder');
  }

  /**
   * Initialize workflow services
   */
  public async initializeWorkflowServices(): Promise<void> {
    // These will be implemented when we create the workflow engine package
    console.log('⚙️ Workflow services registration placeholder');
  }

  /**
   * Initialize job queue services
   */
  public async initializeJobQueueServices(): Promise<void> {
    const { BullMQJobQueue, BullMQJobProcessor } = await import('../queue');

    this.registerSingleton(TOKENS.JOB_QUEUE, BullMQJobQueue);
    this.registerSingleton(TOKENS.JOB_PROCESSOR, BullMQJobProcessor);

    console.log('🔄 Job queue services registered');
  }

  /**
   * Initialize email services
   */
  public async initializeEmailServices(): Promise<void> {
    // These will be implemented when we create the email service package
    console.log('📧 Email services registration placeholder');
  }

  /**
   * Initialize all services
   * This is the main initialization method called during app startup
   */
  public async initializeAll(config: AppConfig): Promise<void> {
    await this.initializeCoreServices(config);
    await this.initializeDatabaseServices();
    await this.initializeWorkflowServices();
    await this.initializeJobQueueServices();
    await this.initializeEmailServices();

    console.log('🚀 All services initialized successfully');
  }
}

// Export singleton instance
export const diContainer = DIContainer.getInstance();
