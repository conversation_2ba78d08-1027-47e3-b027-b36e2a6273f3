import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { GoodsReceiptNote, GoodsReceiptNoteLine, UUID } from "@/types/p2p";

export type GoodsReceiptProps = {
  receipts: GoodsReceiptNote[];
  onReceive: (grn: GoodsReceiptNote) => void;
};

export function GoodsReceipt({ receipts, onReceive }: GoodsReceiptProps) {
  const [form, setForm] = useState<{ poId: string; lines: string }>(() => ({ poId: "", lines: "" }));

  function handleReceive(e: React.FormEvent) {
    e.preventDefault();
    if (!form.poId) return;
    const lines: GoodsReceiptNoteLine[] = form.lines.split("\n").map((l) => {
      const [productId, qty] = l.split(",").map(s => s.trim());
      return { id: crypto.randomUUID(), productId, quantityReceived: Number(qty || 0) };
    });
    onReceive({ id: crypto.randomUUID(), poId: form.poId, lines });
    setForm({ poId: "", lines: "" });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Goods Receipt</CardTitle>
        <CardDescription>Record received quantities against a PO</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleReceive} className="grid grid-cols-1 md:grid-cols-5 gap-2">
          <Input placeholder="PO ID" value={form.poId} onChange={e => setForm({ ...form, poId: e.target.value })} />
          <Input placeholder="Lines: productId,qty per line" value={form.lines} onChange={e => setForm({ ...form, lines: e.target.value })} className="md:col-span-3" />
          <Button type="submit">Receive</Button>
        </form>

        <div className="space-y-2">
          {receipts.map(grn => (
            <div key={grn.id} className="border rounded-md p-3">
              <div className="font-medium">GRN #{grn.id.slice(0, 8)} • PO {grn.poId}</div>
              <div className="text-xs text-muted-foreground">Lines: {grn.lines.length}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

