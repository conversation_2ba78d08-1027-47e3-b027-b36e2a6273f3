import { z } from 'zod';

// Supplier Information
export const SupplierSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url().optional(),
  contactPerson: z.string().optional(),
  certifications: z.array(z.string()).default([]),
  esgRating: z.number().min(0).max(10).optional(),
  region: z.string().optional(),
  categories: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Supplier = z.infer<typeof SupplierSchema>;

// RFQ Line Item
export const RFQLineItemSchema = z.object({
  id: z.string(),
  description: z.string(),
  quantity: z.number().positive(),
  unit: z.string(),
  specifications: z.string().optional(),
  estimatedPrice: z.number().positive().optional(),
  category: z.string().optional(),
  deliveryDate: z.date().optional(),
  metadata: z.record(z.any()).default({}),
});

export type RFQLineItem = z.infer<typeof RFQLineItemSchema>;

// RFQ Document
export const RFQDocumentSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  requesterName: z.string(),
  requesterEmail: z.string().email(),
  department: z.string().optional(),
  budgetRange: z.object({
    min: z.number().positive().optional(),
    max: z.number().positive().optional(),
  }).optional(),
  lineItems: z.array(RFQLineItemSchema),
  deliveryAddress: z.string().optional(),
  deliveryDate: z.date().optional(),
  paymentTerms: z.string().optional(),
  specialRequirements: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  suppliers: z.array(z.string()).default([]), // Supplier IDs
  status: z.enum(['draft', 'sent', 'responses_received', 'evaluated', 'awarded', 'cancelled']),
  createdAt: z.date(),
  updatedAt: z.date(),
  sentAt: z.date().optional(),
  responseDeadline: z.date().optional(),
  metadata: z.record(z.any()).default({}),
});

export type RFQDocument = z.infer<typeof RFQDocumentSchema>;

// Supplier Proposal
export const SupplierProposalSchema = z.object({
  id: z.string(),
  rfqId: z.string(),
  supplierId: z.string(),
  lineItemResponses: z.array(z.object({
    lineItemId: z.string(),
    unitPrice: z.number().positive(),
    totalPrice: z.number().positive(),
    deliveryDate: z.date().optional(),
    comments: z.string().optional(),
  })),
  totalAmount: z.number().positive(),
  validityPeriod: z.number().positive().optional(), // days
  paymentTerms: z.string().optional(),
  deliveryTerms: z.string().optional(),
  warranties: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  submittedAt: z.date(),
  status: z.enum(['submitted', 'under_review', 'accepted', 'rejected']),
  evaluationScore: z.number().min(0).max(100).optional(),
  evaluationNotes: z.string().optional(),
  metadata: z.record(z.any()).default({}),
});

export type SupplierProposal = z.infer<typeof SupplierProposalSchema>;

// Procurement Request
export const ProcurementRequestSchema = z.object({
  id: z.string(),
  type: z.enum(['supplier_search', 'rfq_generation', 'invoice_processing', 'contract_analysis']),
  title: z.string(),
  description: z.string(),
  requesterId: z.string(),
  department: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  budget: z.number().positive().optional(),
  region: z.string().optional(),
  requirements: z.record(z.any()).default({}),
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']),
  workflowExecutionId: z.string().optional(),
  results: z.record(z.any()).default({}),
  createdAt: z.date(),
  updatedAt: z.date(),
  completedAt: z.date().optional(),
});

export type ProcurementRequest = z.infer<typeof ProcurementRequestSchema>;

// Market Research Data
export const MarketResearchDataSchema = z.object({
  id: z.string(),
  category: z.string(),
  region: z.string(),
  priceRange: z.object({
    min: z.number().positive(),
    max: z.number().positive(),
    average: z.number().positive(),
    currency: z.string().default('USD'),
  }),
  suppliers: z.array(z.string()), // Supplier IDs
  trends: z.array(z.string()).default([]),
  complianceRequirements: z.array(z.string()).default([]),
  sustainability: z.object({
    esgScore: z.number().min(0).max(10).optional(),
    certifications: z.array(z.string()).default([]),
    carbonFootprint: z.string().optional(),
  }).optional(),
  lastUpdated: z.date(),
  sources: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
});

export type MarketResearchData = z.infer<typeof MarketResearchDataSchema>;
