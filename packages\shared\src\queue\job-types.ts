import { z } from 'zod';

// Job Priority
export const JobPrioritySchema = z.enum(['low', 'normal', 'high', 'critical']);
export type JobPriority = z.infer<typeof JobPrioritySchema>;

// Job Status
export const JobStatusSchema = z.enum([
  'waiting',
  'active',
  'completed',
  'failed',
  'delayed',
  'paused',
  'cancelled'
]);
export type JobStatus = z.infer<typeof JobStatusSchema>;

// Base Job Data
export const BaseJobDataSchema = z.object({
  id: z.string(),
  type: z.string(),
  priority: JobPrioritySchema.default('normal'),
  attempts: z.number().default(0),
  maxAttempts: z.number().default(3),
  delay: z.number().optional(),
  timeout: z.number().optional(),
  metadata: z.record(z.any()).default({}),
});

export type BaseJobData = z.infer<typeof BaseJobDataSchema>;

// Workflow Execution Job
export const WorkflowExecutionJobDataSchema = BaseJobDataSchema.extend({
  type: z.literal('workflow_execution'),
  workflowId: z.string(),
  input: z.record(z.any()),
  variables: z.record(z.any()).default({}),
  userId: z.string().optional(),
  executionId: z.string(),
});

export type WorkflowExecutionJobData = z.infer<typeof WorkflowExecutionJobDataSchema>;

// Email Job
export const EmailJobDataSchema = BaseJobDataSchema.extend({
  type: z.literal('send_email'),
  to: z.array(z.string()),
  subject: z.string(),
  template: z.string().optional(),
  templateData: z.record(z.any()).default({}),
  attachments: z.array(z.object({
    filename: z.string(),
    content: z.string(),
    contentType: z.string(),
  })).default([]),
});

export type EmailJobData = z.infer<typeof EmailJobDataSchema>;

// Document Processing Job
export const DocumentProcessingJobDataSchema = BaseJobDataSchema.extend({
  type: z.literal('process_document'),
  documentId: z.string(),
  filePath: z.string(),
  extractText: z.boolean().default(true),
  generateEmbeddings: z.boolean().default(true),
  chunkSize: z.number().default(1000),
  chunkOverlap: z.number().default(200),
});

export type DocumentProcessingJobData = z.infer<typeof DocumentProcessingJobDataSchema>;

// Bulk RFQ Job
export const BulkRFQJobDataSchema = BaseJobDataSchema.extend({
  type: z.literal('send_bulk_rfq'),
  rfqId: z.string(),
  supplierIds: z.array(z.string()),
  templateId: z.string(),
  attachments: z.array(z.string()).default([]),
  sendDelay: z.number().default(0), // Delay between emails in ms
});

export type BulkRFQJobData = z.infer<typeof BulkRFQJobDataSchema>;

// Union of all job data types
export type JobData = 
  | WorkflowExecutionJobData 
  | EmailJobData 
  | DocumentProcessingJobData 
  | BulkRFQJobData;

// Job Result
export const JobResultSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  executionTime: z.number(),
  completedAt: z.date(),
  metadata: z.record(z.any()).default({}),
});

export type JobResult = z.infer<typeof JobResultSchema>;

// Job Progress
export const JobProgressSchema = z.object({
  percentage: z.number().min(0).max(100),
  message: z.string().optional(),
  data: z.any().optional(),
  timestamp: z.date(),
});

export type JobProgress = z.infer<typeof JobProgressSchema>;

// Job Options
export const JobOptionsSchema = z.object({
  priority: JobPrioritySchema.default('normal'),
  delay: z.number().optional(),
  attempts: z.number().default(3),
  backoff: z.object({
    type: z.enum(['fixed', 'exponential']).default('exponential'),
    delay: z.number().default(2000),
  }).optional(),
  removeOnComplete: z.number().default(100),
  removeOnFail: z.number().default(50),
  timeout: z.number().optional(),
});

export type JobOptions = z.infer<typeof JobOptionsSchema>;
