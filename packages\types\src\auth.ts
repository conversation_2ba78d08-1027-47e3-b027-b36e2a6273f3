import { z } from 'zod';

// User Role
export const UserRoleSchema = z.enum([
  'admin',
  'procurement_manager',
  'procurement_officer',
  'approver',
  'requester',
  'viewer'
]);

export type UserRole = z.infer<typeof UserRoleSchema>;

// User
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  role: UserRoleSchema,
  department: z.string().optional(),
  permissions: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  lastLoginAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

// JWT Payload
export const JWTPayloadSchema = z.object({
  sub: z.string(), // user ID
  email: z.string().email(),
  role: UserRoleSchema,
  permissions: z.array(z.string()).default([]),
  iat: z.number(),
  exp: z.number(),
});

export type JWTPayload = z.infer<typeof JWTPayloadSchema>;

// Login Request
export const LoginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

export type LoginRequest = z.infer<typeof LoginRequestSchema>;

// Login Response
export const LoginResponseSchema = z.object({
  token: z.string(),
  refreshToken: z.string(),
  user: UserSchema,
  expiresAt: z.date(),
});

export type LoginResponse = z.infer<typeof LoginResponseSchema>;

// Permission
export const PermissionSchema = z.enum([
  'workflow:execute',
  'workflow:create',
  'workflow:edit',
  'workflow:delete',
  'rfq:create',
  'rfq:edit',
  'rfq:send',
  'rfq:approve',
  'supplier:create',
  'supplier:edit',
  'supplier:delete',
  'user:create',
  'user:edit',
  'user:delete',
  'admin:all'
]);

export type Permission = z.infer<typeof PermissionSchema>;
