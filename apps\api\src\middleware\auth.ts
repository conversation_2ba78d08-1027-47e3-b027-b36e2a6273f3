import { Context, Next } from 'hono';
import { AuthenticationError, AuthorizationError } from '@procuregpt/shared';
import { diContainer, TOKENS } from '@procuregpt/shared';
import type { JWTPayload, Permission } from '@procuregpt/types';

// JWT service interface (will be implemented in auth service)
interface JWTService {
  verifyToken(token: string): Promise<JWTPayload>;
}

export async function authMiddleware(c: Context, next: Next) {
  try {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // For now, we'll implement a basic JWT verification
    // This will be replaced with proper JWT service from DI container
    if (!token) {
      throw new AuthenticationError('Token is required');
    }

    // TODO: Replace with actual JWT service
    // const jwtService = diContainer.resolve<JWTService>(TOKENS.JWT_SERVICE);
    // const payload = await jwtService.verifyToken(token);
    
    // Mock user for now - will be replaced with real JWT verification
    const mockUser = {
      sub: 'user-123',
      email: '<EMAIL>',
      role: 'procurement_officer' as const,
      permissions: ['workflow:execute', 'rfq:create'] as Permission[],
      iat: Date.now(),
      exp: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
    };

    // Add user to context
    c.set('user', mockUser);
    
    await next();
  } catch (error) {
    if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
      throw error;
    }
    throw new AuthenticationError('Invalid token');
  }
}

/**
 * Middleware to check if user has required permission
 */
export function requirePermission(permission: Permission) {
  return async (c: Context, next: Next) => {
    const user = c.get('user') as JWTPayload;
    
    if (!user) {
      throw new AuthenticationError('User not authenticated');
    }

    if (!user.permissions.includes(permission) && !user.permissions.includes('admin:all')) {
      throw new AuthorizationError(`Permission required: ${permission}`);
    }

    await next();
  };
}

/**
 * Middleware to check if user has required role
 */
export function requireRole(role: string) {
  return async (c: Context, next: Next) => {
    const user = c.get('user') as JWTPayload;
    
    if (!user) {
      throw new AuthenticationError('User not authenticated');
    }

    if (user.role !== role && user.role !== 'admin') {
      throw new AuthorizationError(`Role required: ${role}`);
    }

    await next();
  };
}
