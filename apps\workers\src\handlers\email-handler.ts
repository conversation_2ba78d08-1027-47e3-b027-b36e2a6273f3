import { Job } from 'bullmq';
import { diContainer, TOKENS } from '@procuregpt/shared';
import type { Logger } from '@procuregpt/shared';
import type { EmailJobData, JobResult } from '@procuregpt/shared';

export class EmailJobHandler {
  private logger: Logger;

  constructor() {
    this.logger = diContainer.resolve<Logger>(TOKENS.LOGGER);
  }

  async handle(job: Job<EmailJobData>): Promise<JobResult> {
    const { to, subject, template, templateData, attachments } = job.data;
    
    this.logger.info('Processing email job', {
      jobId: job.id,
      recipients: to.length,
      subject,
      template,
      attachmentCount: attachments.length
    });

    try {
      // Update progress
      await job.updateProgress({
        percentage: 10,
        message: 'Preparing email',
        timestamp: new Date(),
      });

      // TODO: Get email service from DI container
      // const emailService = diContainer.resolve(TOKENS.EMAIL_SERVICE);

      await job.updateProgress({
        percentage: 30,
        message: 'Rendering email template',
        timestamp: new Date(),
      });

      // Mock email sending for now
      await this.mockSendEmail(to, subject, template, templateData, attachments);

      await job.updateProgress({
        percentage: 90,
        message: 'Email sent successfully',
        timestamp: new Date(),
      });

      this.logger.info('Email sent successfully', {
        jobId: job.id,
        recipients: to.length,
        subject
      });

      return {
        success: true,
        data: {
          messageId: `mock-${Date.now()}`,
          recipients: to,
          subject,
          sentAt: new Date(),
        },
        executionTime: 0,
        completedAt: new Date(),
        metadata: {
          recipientCount: to.length,
          attachmentCount: attachments.length,
        },
      };

    } catch (error) {
      this.logger.error('Email job failed', error as Error, {
        jobId: job.id,
        recipients: to.length,
        subject
      });

      throw error;
    }
  }

  private async mockSendEmail(
    to: string[],
    subject: string,
    template?: string,
    templateData?: Record<string, any>,
    attachments?: Array<{ filename: string; content: string; contentType: string }>
  ): Promise<void> {
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    this.logger.debug('Mock email sent', {
      to,
      subject,
      template,
      templateDataKeys: templateData ? Object.keys(templateData) : [],
      attachmentCount: attachments?.length || 0
    });
  }
}
