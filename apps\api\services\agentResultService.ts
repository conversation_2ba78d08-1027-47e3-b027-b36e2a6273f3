import crypto from 'crypto';
import { getPool, isPostgreSQLAvailable } from '../database/config.js';
import { EmbeddingService } from './embeddingService.js';
import {
  AgentResult,
  AgentExecutionStep,
  AgentResultTag,
  CreateAgentResult,
  UpdateAgentResult,
  CreateAgentExecutionStep,
  UpdateAgentExecutionStep,
  CreateAgentResultTag,
  AgentResultSearch,
  VectorSearch,
  AgentResultListResponse,
  VectorSearchResponse,
  AgentExecutionStatus,
  StepExecutionStatus,
} from '../database/types.js';

export class AgentResultService {
  private static instance: AgentResultService;
  private pool: any;
  private embeddingService: EmbeddingService;
  private isAvailable: boolean;

  private constructor() {
    this.isAvailable = isPostgreSQLAvailable();
    if (this.isAvailable) {
      this.pool = getPool();
    } else {
      console.warn('⚠️ AgentResultService: PostgreSQL not available - service will be disabled');
    }
    this.embeddingService = EmbeddingService.getInstance();
  }

  static getInstance(): AgentResultService {
    if (!AgentResultService.instance) {
      AgentResultService.instance = new AgentResultService();
    }
    return AgentResultService.instance;
  }

  // Check if the service is available
  isServiceAvailable(): boolean {
    return this.isAvailable;
  }

  // Throw error if service is not available
  private ensureAvailable(): void {
    if (!this.isAvailable) {
      throw new Error('AgentResultService is not available. PostgreSQL client (pg) is not installed. Install with: npm install pg @types/pg');
    }
  }

  private generateInputHash(input: string): string {
    return crypto.createHash('sha256').update(input).digest('hex');
  }

  private formatEmbeddingForPostgres(embedding: number[]): string {
    return `[${embedding.join(',')}]`;
  }

  private parseEmbeddingFromPostgres(embedding: string | null): number[] | null {
    if (!embedding) return null;
    try {
      return JSON.parse(embedding);
    } catch {
      return null;
    }
  }

  // Create a new agent result
  async createAgentResult(data: CreateAgentResult): Promise<AgentResult> {
    this.ensureAvailable();
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      // Generate embeddings if not provided
      let inputEmbedding = data.input_embedding;
      let outputEmbedding = data.output_embedding;
      let combinedEmbedding = data.combined_embedding;

      if (!inputEmbedding || !outputEmbedding || !combinedEmbedding) {
        const embeddings = await this.embeddingService.generateAgentEmbeddings({
          input: data.input_text,
          queryAnalysis: data.query_analysis,
          recommendations: data.recommendations,
          documents: data.documents,
          actionPlan: data.action_plan,
        });

        inputEmbedding = inputEmbedding || embeddings.inputEmbedding;
        outputEmbedding = outputEmbedding || embeddings.outputEmbedding;
        combinedEmbedding = combinedEmbedding || embeddings.combinedEmbedding;
      }

      const query = `
        INSERT INTO agent_results (
          workflow_id, input_text, input_hash, status,
          query_analysis, market_research, recommendations, documents, action_plan, rfq_data, git_commit_info,
          error_message, error_trace, partial_results,
          workflow_config, workflow_steps, total_steps, completed_steps, failed_steps,
          system_status, region, agent_version,
          input_embedding, output_embedding, combined_embedding
        ) VALUES (
          $1, $2, $3, $4,
          $5, $6, $7, $8, $9, $10, $11,
          $12, $13, $14,
          $15, $16, $17, $18, $19,
          $20, $21, $22,
          $23, $24, $25
        ) RETURNING *
      `;

      const values = [
        data.workflow_id,
        data.input_text,
        this.generateInputHash(data.input_text),
        data.status,
        data.query_analysis ? JSON.stringify(data.query_analysis) : null,
        data.market_research ? JSON.stringify(data.market_research) : null,
        data.recommendations ? JSON.stringify(data.recommendations) : null,
        data.documents ? JSON.stringify(data.documents) : null,
        data.action_plan ? JSON.stringify(data.action_plan) : null,
        data.rfq_data ? JSON.stringify(data.rfq_data) : null,
        data.git_commit_info ? JSON.stringify(data.git_commit_info) : null,
        data.error_message,
        data.error_trace ? JSON.stringify(data.error_trace) : null,
        data.partial_results,
        data.workflow_config ? JSON.stringify(data.workflow_config) : null,
        data.workflow_steps ? JSON.stringify(data.workflow_steps) : null,
        data.total_steps,
        data.completed_steps,
        data.failed_steps,
        data.system_status ? JSON.stringify(data.system_status) : null,
        data.region,
        data.agent_version,
        inputEmbedding ? this.formatEmbeddingForPostgres(inputEmbedding) : null,
        outputEmbedding ? this.formatEmbeddingForPostgres(outputEmbedding) : null,
        combinedEmbedding ? this.formatEmbeddingForPostgres(combinedEmbedding) : null,
      ];

      const result = await client.query(query, values);
      await client.query('COMMIT');

      const row = result.rows[0];
      return this.mapRowToAgentResult(row);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Update an existing agent result
  async updateAgentResult(workflowId: string, data: UpdateAgentResult): Promise<AgentResult | null> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      // Build dynamic update query
      const updateFields: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (data.status !== undefined) {
        updateFields.push(`status = $${paramIndex++}`);
        values.push(data.status);
      }

      if (data.completed_at !== undefined) {
        updateFields.push(`completed_at = $${paramIndex++}`);
        values.push(data.completed_at);
      }

      if (data.execution_time_ms !== undefined) {
        updateFields.push(`execution_time_ms = $${paramIndex++}`);
        values.push(data.execution_time_ms);
      }

      if (data.query_analysis !== undefined) {
        updateFields.push(`query_analysis = $${paramIndex++}`);
        values.push(data.query_analysis ? JSON.stringify(data.query_analysis) : null);
      }

      if (data.market_research !== undefined) {
        updateFields.push(`market_research = $${paramIndex++}`);
        values.push(data.market_research ? JSON.stringify(data.market_research) : null);
      }

      if (data.recommendations !== undefined) {
        updateFields.push(`recommendations = $${paramIndex++}`);
        values.push(data.recommendations ? JSON.stringify(data.recommendations) : null);
      }

      if (data.documents !== undefined) {
        updateFields.push(`documents = $${paramIndex++}`);
        values.push(data.documents ? JSON.stringify(data.documents) : null);
      }

      if (data.action_plan !== undefined) {
        updateFields.push(`action_plan = $${paramIndex++}`);
        values.push(data.action_plan ? JSON.stringify(data.action_plan) : null);
      }

      if (data.rfq_data !== undefined) {
        updateFields.push(`rfq_data = $${paramIndex++}`);
        values.push(data.rfq_data ? JSON.stringify(data.rfq_data) : null);
      }

      if (data.git_commit_info !== undefined) {
        updateFields.push(`git_commit_info = $${paramIndex++}`);
        values.push(data.git_commit_info ? JSON.stringify(data.git_commit_info) : null);
      }

      if (data.error_message !== undefined) {
        updateFields.push(`error_message = $${paramIndex++}`);
        values.push(data.error_message);
      }

      if (data.error_trace !== undefined) {
        updateFields.push(`error_trace = $${paramIndex++}`);
        values.push(data.error_trace ? JSON.stringify(data.error_trace) : null);
      }

      if (data.partial_results !== undefined) {
        updateFields.push(`partial_results = $${paramIndex++}`);
        values.push(data.partial_results);
      }

      if (data.workflow_config !== undefined) {
        updateFields.push(`workflow_config = $${paramIndex++}`);
        values.push(data.workflow_config ? JSON.stringify(data.workflow_config) : null);
      }

      if (data.workflow_steps !== undefined) {
        updateFields.push(`workflow_steps = $${paramIndex++}`);
        values.push(data.workflow_steps ? JSON.stringify(data.workflow_steps) : null);
      }

      if (data.total_steps !== undefined) {
        updateFields.push(`total_steps = $${paramIndex++}`);
        values.push(data.total_steps);
      }

      if (data.completed_steps !== undefined) {
        updateFields.push(`completed_steps = $${paramIndex++}`);
        values.push(data.completed_steps);
      }

      if (data.failed_steps !== undefined) {
        updateFields.push(`failed_steps = $${paramIndex++}`);
        values.push(data.failed_steps);
      }

      if (data.system_status !== undefined) {
        updateFields.push(`system_status = $${paramIndex++}`);
        values.push(data.system_status ? JSON.stringify(data.system_status) : null);
      }

      if (data.agent_version !== undefined) {
        updateFields.push(`agent_version = $${paramIndex++}`);
        values.push(data.agent_version);
      }

      // Handle embeddings
      if (data.input_embedding !== undefined) {
        updateFields.push(`input_embedding = $${paramIndex++}`);
        values.push(data.input_embedding ? this.formatEmbeddingForPostgres(data.input_embedding) : null);
      }

      if (data.output_embedding !== undefined) {
        updateFields.push(`output_embedding = $${paramIndex++}`);
        values.push(data.output_embedding ? this.formatEmbeddingForPostgres(data.output_embedding) : null);
      }

      if (data.combined_embedding !== undefined) {
        updateFields.push(`combined_embedding = $${paramIndex++}`);
        values.push(data.combined_embedding ? this.formatEmbeddingForPostgres(data.combined_embedding) : null);
      }

      if (updateFields.length === 0) {
        await client.query('ROLLBACK');
        return null;
      }

      const query = `
        UPDATE agent_results 
        SET ${updateFields.join(', ')}, updated_at = NOW()
        WHERE workflow_id = $${paramIndex}
        RETURNING *
      `;

      values.push(workflowId);

      const result = await client.query(query, values);
      await client.query('COMMIT');

      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRowToAgentResult(result.rows[0]);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  private mapRowToAgentResult(row: any): AgentResult {
    return {
      id: row.id,
      workflow_id: row.workflow_id,
      input_text: row.input_text,
      input_hash: row.input_hash,
      status: row.status as AgentExecutionStatus,
      started_at: new Date(row.started_at),
      completed_at: row.completed_at ? new Date(row.completed_at) : null,
      execution_time_ms: row.execution_time_ms,
      query_analysis: row.query_analysis,
      market_research: row.market_research,
      recommendations: row.recommendations,
      documents: row.documents,
      action_plan: row.action_plan,
      rfq_data: row.rfq_data,
      git_commit_info: row.git_commit_info,
      error_message: row.error_message,
      error_trace: row.error_trace,
      partial_results: row.partial_results || false,
      workflow_config: row.workflow_config,
      workflow_steps: row.workflow_steps,
      total_steps: row.total_steps,
      completed_steps: row.completed_steps,
      failed_steps: row.failed_steps,
      system_status: row.system_status,
      region: row.region || 'Mangaluru, India',
      agent_version: row.agent_version,
      input_embedding: this.parseEmbeddingFromPostgres(row.input_embedding),
      output_embedding: this.parseEmbeddingFromPostgres(row.output_embedding),
      combined_embedding: this.parseEmbeddingFromPostgres(row.combined_embedding),
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
    };
  }

  // Get agent result by workflow ID
  async getAgentResult(workflowId: string): Promise<AgentResult | null> {
    const query = 'SELECT * FROM agent_results WHERE workflow_id = $1';
    const result = await this.pool.query(query, [workflowId]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapRowToAgentResult(result.rows[0]);
  }

  // Get agent result by ID
  async getAgentResultById(id: string): Promise<AgentResult | null> {
    const query = 'SELECT * FROM agent_results WHERE id = $1';
    const result = await this.pool.query(query, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return this.mapRowToAgentResult(result.rows[0]);
  }

  // Delete agent result
  async deleteAgentResult(workflowId: string): Promise<boolean> {
    const query = 'DELETE FROM agent_results WHERE workflow_id = $1';
    const result = await this.pool.query(query, [workflowId]);
    return result.rowCount > 0;
  }

  // Search agent results with traditional filters
  async searchAgentResults(searchParams: AgentResultSearch): Promise<AgentResultListResponse> {
    const conditions: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (searchParams.status) {
      conditions.push(`status = $${paramIndex++}`);
      values.push(searchParams.status);
    }

    if (searchParams.region) {
      conditions.push(`region = $${paramIndex++}`);
      values.push(searchParams.region);
    }

    if (searchParams.start_date) {
      conditions.push(`started_at >= $${paramIndex++}`);
      values.push(searchParams.start_date);
    }

    if (searchParams.end_date) {
      conditions.push(`started_at <= $${paramIndex++}`);
      values.push(searchParams.end_date);
    }

    if (searchParams.query) {
      conditions.push(`(
        input_text ILIKE $${paramIndex} OR
        query_analysis::text ILIKE $${paramIndex} OR
        recommendations::text ILIKE $${paramIndex}
      )`);
      values.push(`%${searchParams.query}%`);
      paramIndex++;
    }

    // Handle tags filter
    if (searchParams.tags && searchParams.tags.length > 0) {
      conditions.push(`id IN (
        SELECT DISTINCT result_id FROM agent_result_tags
        WHERE tag_name = ANY($${paramIndex})
      )`);
      values.push(searchParams.tags);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Count total results
    const countQuery = `SELECT COUNT(*) as total FROM agent_results ${whereClause}`;
    const countResult = await this.pool.query(countQuery, values);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const orderBy = `ORDER BY ${searchParams.sort_by} ${searchParams.sort_order.toUpperCase()}`;
    const pagination = `LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
    values.push(searchParams.limit, searchParams.offset);

    const query = `
      SELECT * FROM agent_results
      ${whereClause}
      ${orderBy}
      ${pagination}
    `;

    const result = await this.pool.query(query, values);
    const results = result.rows.map(row => this.mapRowToAgentResult(row));

    return {
      results,
      total,
      limit: searchParams.limit,
      offset: searchParams.offset,
      has_more: searchParams.offset + searchParams.limit < total,
    };
  }

  // Vector-based similarity search
  async vectorSearch(searchParams: VectorSearch): Promise<VectorSearchResponse> {
    const embeddingColumn = `${searchParams.embedding_type}_embedding`;
    const embeddingVector = this.formatEmbeddingForPostgres(searchParams.embedding);

    const query = `
      SELECT *,
             1 - (${embeddingColumn} <=> $1::vector) as similarity_score
      FROM agent_results
      WHERE ${embeddingColumn} IS NOT NULL
        AND 1 - (${embeddingColumn} <=> $1::vector) >= $2
      ORDER BY ${embeddingColumn} <=> $1::vector
      LIMIT $3
    `;

    const values = [
      embeddingVector,
      searchParams.similarity_threshold,
      searchParams.limit,
    ];

    const result = await this.pool.query(query, values);

    const results = result.rows.map(row => ({
      ...this.mapRowToAgentResult(row),
      similarity_score: parseFloat(row.similarity_score),
    }));

    return {
      results,
      query_embedding: searchParams.embedding,
      total_found: results.length,
    };
  }

  // Find similar agent results based on input text
  async findSimilarResults(inputText: string, limit = 10, threshold = 0.7): Promise<VectorSearchResponse> {
    const embedding = await this.embeddingService.generateEmbedding(inputText);

    return this.vectorSearch({
      embedding,
      embedding_type: 'input',
      similarity_threshold: threshold,
      limit,
      include_metadata: true,
    });
  }

  // Get recent agent results
  async getRecentResults(limit = 20): Promise<AgentResult[]> {
    const query = `
      SELECT * FROM agent_results
      ORDER BY started_at DESC
      LIMIT $1
    `;

    const result = await this.pool.query(query, [limit]);
    return result.rows.map(row => this.mapRowToAgentResult(row));
  }

  // Get agent results by status
  async getResultsByStatus(status: AgentExecutionStatus, limit = 50): Promise<AgentResult[]> {
    const query = `
      SELECT * FROM agent_results
      WHERE status = $1
      ORDER BY started_at DESC
      LIMIT $2
    `;

    const result = await this.pool.query(query, [status, limit]);
    return result.rows.map(row => this.mapRowToAgentResult(row));
  }

  // Get statistics about agent results
  async getResultsStats(): Promise<{
    total: number;
    by_status: Record<string, number>;
    recent_24h: number;
    avg_execution_time_ms: number;
  }> {
    const queries = [
      'SELECT COUNT(*) as total FROM agent_results',
      'SELECT status, COUNT(*) as count FROM agent_results GROUP BY status',
      'SELECT COUNT(*) as recent FROM agent_results WHERE started_at >= NOW() - INTERVAL \'24 hours\'',
      'SELECT AVG(execution_time_ms) as avg_time FROM agent_results WHERE execution_time_ms IS NOT NULL',
    ];

    const [totalResult, statusResult, recentResult, avgTimeResult] = await Promise.all(
      queries.map(query => this.pool.query(query))
    );

    const byStatus: Record<string, number> = {};
    statusResult.rows.forEach(row => {
      byStatus[row.status] = parseInt(row.count);
    });

    return {
      total: parseInt(totalResult.rows[0].total),
      by_status: byStatus,
      recent_24h: parseInt(recentResult.rows[0].recent),
      avg_execution_time_ms: parseFloat(avgTimeResult.rows[0].avg_time) || 0,
    };
  }

  // Execution step management methods

  // Create execution step
  async createExecutionStep(data: CreateAgentExecutionStep): Promise<AgentExecutionStep> {
    const query = `
      INSERT INTO agent_execution_steps (
        result_id, workflow_id, step_id, step_name, step_order, status,
        started_at, completed_at, duration_ms, attempts,
        result_data, error_message, error_details
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
      ) RETURNING *
    `;

    const values = [
      data.result_id,
      data.workflow_id,
      data.step_id,
      data.step_name,
      data.step_order,
      data.status,
      data.started_at,
      data.completed_at,
      data.duration_ms,
      data.attempts,
      data.result_data ? JSON.stringify(data.result_data) : null,
      data.error_message,
      data.error_details ? JSON.stringify(data.error_details) : null,
    ];

    const result = await this.pool.query(query, values);
    return this.mapRowToExecutionStep(result.rows[0]);
  }

  // Update execution step
  async updateExecutionStep(stepId: string, resultId: string, data: UpdateAgentExecutionStep): Promise<AgentExecutionStep | null> {
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (data.status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`);
      values.push(data.status);
    }

    if (data.started_at !== undefined) {
      updateFields.push(`started_at = $${paramIndex++}`);
      values.push(data.started_at);
    }

    if (data.completed_at !== undefined) {
      updateFields.push(`completed_at = $${paramIndex++}`);
      values.push(data.completed_at);
    }

    if (data.duration_ms !== undefined) {
      updateFields.push(`duration_ms = $${paramIndex++}`);
      values.push(data.duration_ms);
    }

    if (data.attempts !== undefined) {
      updateFields.push(`attempts = $${paramIndex++}`);
      values.push(data.attempts);
    }

    if (data.result_data !== undefined) {
      updateFields.push(`result_data = $${paramIndex++}`);
      values.push(data.result_data ? JSON.stringify(data.result_data) : null);
    }

    if (data.error_message !== undefined) {
      updateFields.push(`error_message = $${paramIndex++}`);
      values.push(data.error_message);
    }

    if (data.error_details !== undefined) {
      updateFields.push(`error_details = $${paramIndex++}`);
      values.push(data.error_details ? JSON.stringify(data.error_details) : null);
    }

    if (updateFields.length === 0) {
      return null;
    }

    const query = `
      UPDATE agent_execution_steps
      SET ${updateFields.join(', ')}, updated_at = NOW()
      WHERE step_id = $${paramIndex++} AND result_id = $${paramIndex}
      RETURNING *
    `;

    values.push(stepId, resultId);

    const result = await this.pool.query(query, values);

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapRowToExecutionStep(result.rows[0]);
  }

  // Get execution steps for a result
  async getExecutionSteps(resultId: string): Promise<AgentExecutionStep[]> {
    const query = `
      SELECT * FROM agent_execution_steps
      WHERE result_id = $1
      ORDER BY step_order ASC
    `;

    const result = await this.pool.query(query, [resultId]);
    return result.rows.map(row => this.mapRowToExecutionStep(row));
  }

  private mapRowToExecutionStep(row: any): AgentExecutionStep {
    return {
      id: row.id,
      result_id: row.result_id,
      workflow_id: row.workflow_id,
      step_id: row.step_id,
      step_name: row.step_name,
      step_order: row.step_order,
      status: row.status as StepExecutionStatus,
      started_at: row.started_at ? new Date(row.started_at) : null,
      completed_at: row.completed_at ? new Date(row.completed_at) : null,
      duration_ms: row.duration_ms,
      attempts: row.attempts || 1,
      result_data: row.result_data,
      error_message: row.error_message,
      error_details: row.error_details,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
    };
  }

  // Tag management methods

  // Add tag to result
  async addTag(data: CreateAgentResultTag): Promise<AgentResultTag> {
    const query = `
      INSERT INTO agent_result_tags (result_id, tag_name, tag_value, tag_type)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (result_id, tag_name)
      DO UPDATE SET tag_value = EXCLUDED.tag_value, tag_type = EXCLUDED.tag_type
      RETURNING *
    `;

    const values = [data.result_id, data.tag_name, data.tag_value, data.tag_type];
    const result = await this.pool.query(query, values);
    return this.mapRowToTag(result.rows[0]);
  }

  // Get tags for a result
  async getTags(resultId: string): Promise<AgentResultTag[]> {
    const query = 'SELECT * FROM agent_result_tags WHERE result_id = $1 ORDER BY tag_name';
    const result = await this.pool.query(query, [resultId]);
    return result.rows.map(row => this.mapRowToTag(row));
  }

  // Remove tag from result
  async removeTag(resultId: string, tagName: string): Promise<boolean> {
    const query = 'DELETE FROM agent_result_tags WHERE result_id = $1 AND tag_name = $2';
    const result = await this.pool.query(query, [resultId, tagName]);
    return result.rowCount > 0;
  }

  private mapRowToTag(row: any): AgentResultTag {
    return {
      id: row.id,
      result_id: row.result_id,
      tag_name: row.tag_name,
      tag_value: row.tag_value,
      tag_type: row.tag_type,
      created_at: new Date(row.created_at),
    };
  }
}
