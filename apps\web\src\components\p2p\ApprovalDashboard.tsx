import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Check, X } from "lucide-react";
import type { PurchaseRequisition } from "@/types/p2p";

export function ApprovalDashboard({
  items = [],
  onDecision,
}: {
  items?: PurchaseRequisition[];
  onDecision: (id: string, decision: "approved" | "rejected", comment?: string) => void;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Approvals</CardTitle>
        <CardDescription>
          Review and take action on pending requisitions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.length === 0 && (
          <div className="text-sm text-muted-foreground">No pending approvals</div>
        )}
        {items.map((req) => (
          <div key={req.id} className="p-4 border rounded-lg space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Requisition {req.id.slice(0, 8)}</div>
                <div className="text-sm text-muted-foreground">
                  {req.lines.length} items • Total {req.currency} {req.totalAmount.toLocaleString()}
                </div>
              </div>
              <Badge variant="outline">{req.status}</Badge>
            </div>
            <div>
              <Textarea placeholder="Add comment (optional)" id={`comment-${req.id}`} />
            </div>
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  const el = document.getElementById(`comment-${req.id}`) as HTMLTextAreaElement | null;
                  onDecision(req.id, "rejected", el?.value);
                }}
              >
                <X className="h-4 w-4 mr-2" /> Reject
              </Button>
              <Button
                onClick={() => {
                  const el = document.getElementById(`comment-${req.id}`) as HTMLTextAreaElement | null;
                  onDecision(req.id, "approved", el?.value);
                }}
              >
                <Check className="h-4 w-4 mr-2" /> Approve
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

