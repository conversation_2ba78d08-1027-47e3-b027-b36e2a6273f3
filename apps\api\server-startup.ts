#!/usr/bin/env node

import { existsSync } from 'fs';
import { join } from 'path';

interface StartupCheck {
  name: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  critical: boolean;
}

class ServerStartup {
  private checks: StartupCheck[] = [];
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  // Enhanced logging
  private log = {
    info: (message: string) => console.log(`[SERVER-INFO] ${message}`),
    warn: (message: string) => console.warn(`[SERVER-WARN] ${message}`),
    error: (message: string) => console.error(`[SERVER-ERROR] ${message}`),
    success: (message: string) => console.log(`[SERVER-SUCCESS] ${message}`),
  };

  // Check if a package is available
  private checkPackage(packageName: string, critical = true): StartupCheck {
    try {
      const packagePath = join(this.projectRoot, 'node_modules', packageName);
      
      if (existsSync(packagePath)) {
        return {
          name: `Package: ${packageName}`,
          status: 'success',
          message: 'Available',
          critical
        };
      } else {
        return {
          name: `Package: ${packageName}`,
          status: critical ? 'error' : 'warning',
          message: 'Not found in node_modules',
          critical
        };
      }
    } catch (error) {
      return {
        name: `Package: ${packageName}`,
        status: 'error',
        message: `Check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        critical
      };
    }
  }

  // Check environment variables
  private checkEnvironment(): StartupCheck[] {
    const checks: StartupCheck[] = [];
    
    // Critical environment variables for database
    const criticalEnvVars = [
      { name: 'DATABASE_HOST', default: 'localhost' },
      { name: 'DATABASE_NAME', default: 'procure_agent' },
      { name: 'DATABASE_USER', default: 'postgres' }
    ];

    // Optional environment variables
    const optionalEnvVars = [
      'DATABASE_PASSWORD',
      'OPENAI_API_KEY',
      'PERPLEXITY_API_KEY',
      'SMTP_HOST',
      'SMTP_USER'
    ];

    // Check critical variables
    criticalEnvVars.forEach(({ name, default: defaultValue }) => {
      const value = process.env[name];
      if (value) {
        checks.push({
          name: `ENV: ${name}`,
          status: 'success',
          message: 'Set',
          critical: true
        });
      } else {
        checks.push({
          name: `ENV: ${name}`,
          status: 'warning',
          message: `Not set, using default: ${defaultValue}`,
          critical: false
        });
      }
    });

    // Check optional variables
    optionalEnvVars.forEach(name => {
      const value = process.env[name];
      checks.push({
        name: `ENV: ${name}`,
        status: value ? 'success' : 'warning',
        message: value ? 'Set' : 'Not set (optional)',
        critical: false
      });
    });

    return checks;
  }

  // Check database connectivity
  private async checkDatabase(): Promise<StartupCheck> {
    try {
      // Try to import database config
      const { testConnection } = await import('./database/config.js');
      const isConnected = await testConnection();
      
      return {
        name: 'Database Connection',
        status: isConnected ? 'success' : 'error',
        message: isConnected ? 'Connected successfully' : 'Connection failed',
        critical: false // Not critical for startup, but important for functionality
      };
    } catch (error) {
      return {
        name: 'Database Connection',
        status: 'error',
        message: `Cannot test connection: ${error instanceof Error ? error.message : 'Unknown error'}`,
        critical: false
      };
    }
  }

  // Check port availability
  private async checkPort(port: number): Promise<StartupCheck> {
    return new Promise((resolve) => {
      const net = require('net');
      const server = net.createServer();
      
      server.listen(port, () => {
        server.close(() => {
          resolve({
            name: `Port ${port}`,
            status: 'success',
            message: 'Available',
            critical: true
          });
        });
      });
      
      server.on('error', () => {
        resolve({
          name: `Port ${port}`,
          status: 'error',
          message: 'Already in use',
          critical: true
        });
      });
    });
  }

  // Run all startup checks
  async runStartupChecks(): Promise<boolean> {
    this.log.info('🚀 Running server startup checks...\n');

    // Package checks
    const criticalPackages = ['hono', 'tsx', 'dotenv'];
    const optionalPackages = ['pg', '@types/pg', 'zod'];

    criticalPackages.forEach(pkg => {
      this.checks.push(this.checkPackage(pkg, true));
    });

    optionalPackages.forEach(pkg => {
      this.checks.push(this.checkPackage(pkg, false));
    });

    // Environment checks
    this.checks.push(...this.checkEnvironment());

    // Port check (assuming backend runs on 3101)
    const portCheck = await this.checkPort(3101);
    this.checks.push(portCheck);

    // Database check (if pg is available)
    const pgCheck = this.checks.find(c => c.name === 'Package: pg');
    if (pgCheck && pgCheck.status === 'success') {
      const dbCheck = await this.checkDatabase();
      this.checks.push(dbCheck);
    } else {
      this.checks.push({
        name: 'Database Connection',
        status: 'warning',
        message: 'Skipped (pg package not available)',
        critical: false
      });
    }

    // Display results
    this.displayResults();

    // Determine if server can start
    const criticalErrors = this.checks.filter(c => c.critical && c.status === 'error');
    return criticalErrors.length === 0;
  }

  private displayResults(): void {
    console.log('\n📋 Server Startup Check Results:');
    console.log('=================================\n');

    // Group by status
    const success = this.checks.filter(c => c.status === 'success');
    const warnings = this.checks.filter(c => c.status === 'warning');
    const errors = this.checks.filter(c => c.status === 'error');

    // Display successful checks
    if (success.length > 0) {
      console.log('✅ Successful Checks:');
      success.forEach(check => {
        console.log(`   ✅ ${check.name}: ${check.message}`);
      });
      console.log();
    }

    // Display warnings
    if (warnings.length > 0) {
      console.log('⚠️  Warnings:');
      warnings.forEach(check => {
        console.log(`   ⚠️  ${check.name}: ${check.message}`);
      });
      console.log();
    }

    // Display errors
    if (errors.length > 0) {
      console.log('❌ Errors:');
      errors.forEach(check => {
        const severity = check.critical ? '[CRITICAL]' : '[NON-CRITICAL]';
        console.log(`   ❌ ${check.name} ${severity}: ${check.message}`);
      });
      console.log();
    }

    // Summary
    const criticalErrors = errors.filter(c => c.critical);
    const nonCriticalErrors = errors.filter(c => !c.critical);

    console.log('📊 Summary:');
    console.log(`   ✅ Success: ${success.length}`);
    console.log(`   ⚠️  Warnings: ${warnings.length}`);
    console.log(`   ❌ Critical Errors: ${criticalErrors.length}`);
    console.log(`   ❌ Non-Critical Errors: ${nonCriticalErrors.length}`);

    if (criticalErrors.length > 0) {
      console.log('\n🚨 Cannot start server due to critical errors!');
      console.log('\n💡 To fix critical errors:');
      
      criticalErrors.forEach(error => {
        if (error.name.includes('Package:')) {
          console.log(`   - Install missing package: npm install ${error.name.split(': ')[1]}`);
        } else if (error.name.includes('Port')) {
          console.log(`   - Free up the port or change the server port configuration`);
        } else if (error.name.includes('ENV:')) {
          console.log(`   - Set environment variable: ${error.name.split(': ')[1]}`);
        }
      });
    } else {
      console.log('\n🎉 Server can start! (Some warnings may exist but are not critical)');
      
      if (nonCriticalErrors.length > 0 || warnings.length > 0) {
        console.log('\n💡 To improve functionality:');
        
        const pgMissing = this.checks.find(c => c.name === 'Package: pg' && c.status !== 'success');
        if (pgMissing) {
          console.log('   - Install PostgreSQL support: npm install pg @types/pg');
          console.log('   - This will enable result persistence and search features');
        }
        
        const dbError = this.checks.find(c => c.name === 'Database Connection' && c.status === 'error');
        if (dbError) {
          console.log('   - Set up PostgreSQL database and configure connection');
          console.log('   - Run: npm run db:setup (after installing pg)');
        }
      }
    }
  }

  // Start server with graceful error handling
  async startServer(): Promise<void> {
    const canStart = await this.runStartupChecks();
    
    if (!canStart) {
      this.log.error('Server startup aborted due to critical errors');
      process.exit(1);
    }

    try {
      this.log.info('Starting server...');
      
      // Import and start the actual server
      const { app } = await import('./app.js');
      
      const port = process.env.PORT || 3101;
      
      console.log(`\n🚀 Server starting on port ${port}...`);
      
      // Start server with error handling
      const server = Bun.serve({
        port: Number(port),
        fetch: app.fetch,
      });

      this.log.success(`Server running on http://localhost:${port}`);
      this.log.info('Press Ctrl+C to stop the server');

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        this.log.info('Received SIGINT, shutting down gracefully...');
        server.stop();
        process.exit(0);
      });

      process.on('SIGTERM', () => {
        this.log.info('Received SIGTERM, shutting down gracefully...');
        server.stop();
        process.exit(0);
      });

    } catch (error) {
      this.log.error(`Failed to start server: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Provide helpful error messages
      if (error instanceof Error) {
        if (error.message.includes('EADDRINUSE')) {
          this.log.error('💡 Port is already in use. Try a different port or stop the conflicting process');
        } else if (error.message.includes('MODULE_NOT_FOUND')) {
          this.log.error('💡 Missing dependencies. Run: npm install');
        }
      }
      
      process.exit(1);
    }
  }
}

// CLI interface
async function main() {
  const startup = new ServerStartup();
  await startup.startServer();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { ServerStartup };
