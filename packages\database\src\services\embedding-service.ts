import OpenAI from 'openai';
import { <PERSON>, Singleton, Inject, TOKENS } from '@procuregpt/shared';
import type { Logger } from '@procuregpt/shared';

export interface EmbeddingService {
  generateEmbedding(text: string): Promise<number[]>;
  generateEmbeddings(texts: string[]): Promise<number[][]>;
}

@Singleton()
@Service
export class OpenAIEmbeddingService implements EmbeddingService {
  private openai: OpenAI | null = null;

  constructor(
    @Inject(TOKENS.CONFIG) private config: { openaiApiKey?: string },
    @Inject(TOKENS.LOGGER) private logger: Logger
  ) {
    if (config.openaiApiKey) {
      this.openai = new OpenAI({
        apiKey: config.openaiApiKey,
      });
      this.logger.info('OpenAI embedding service initialized');
    } else {
      this.logger.warn('OpenAI API key not provided, embedding service will use mock embeddings');
    }
  }

  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.openai) {
      this.logger.debug('Using mock embedding for text', { textLength: text.length });
      return this.generateMockEmbedding();
    }

    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
      });

      const embedding = response.data[0].embedding;
      this.logger.debug('Generated embedding', { 
        textLength: text.length, 
        embeddingDimensions: embedding.length 
      });

      return embedding;
    } catch (error) {
      this.logger.error('Failed to generate embedding', error as Error, { textLength: text.length });
      // Fallback to mock embedding
      return this.generateMockEmbedding();
    }
  }

  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (!this.openai) {
      this.logger.debug('Using mock embeddings for batch', { count: texts.length });
      return texts.map(() => this.generateMockEmbedding());
    }

    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: texts,
      });

      const embeddings = response.data.map(item => item.embedding);
      this.logger.debug('Generated batch embeddings', { 
        count: texts.length,
        embeddingDimensions: embeddings[0]?.length || 0
      });

      return embeddings;
    } catch (error) {
      this.logger.error('Failed to generate batch embeddings', error as Error, { count: texts.length });
      // Fallback to mock embeddings
      return texts.map(() => this.generateMockEmbedding());
    }
  }

  private generateMockEmbedding(): number[] {
    // Generate a mock 1536-dimensional embedding (same as OpenAI text-embedding-ada-002)
    return new Array(1536).fill(0).map(() => Math.random() - 0.5);
  }
}
