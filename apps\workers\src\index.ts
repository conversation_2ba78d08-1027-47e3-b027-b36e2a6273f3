import 'reflect-metadata';
import { loadConfig } from '@procuregpt/config';
import { diContainer } from '@procuregpt/shared';
import { WorkerManager } from './worker-manager';

async function startWorkers() {
  try {
    console.log('🚀 Starting ProcureGPT Workers...');
    
    // Load configuration
    const config = loadConfig();
    
    // Initialize DI container
    await diContainer.initializeAll(config);
    
    // Create and start worker manager
    const workerManager = new WorkerManager();
    await workerManager.start();
    
    console.log('✅ Workers started successfully');
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Received SIGINT, shutting down workers gracefully...');
      await workerManager.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🛑 Received SIGTERM, shutting down workers gracefully...');
      await workerManager.stop();
      process.exit(0);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', async (error) => {
      console.error('❌ Uncaught exception:', error);
      await workerManager.stop();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      console.error('❌ Unhandled rejection at:', promise, 'reason:', reason);
      await workerManager.stop();
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start workers:', error);
    process.exit(1);
  }
}

startWorkers();
