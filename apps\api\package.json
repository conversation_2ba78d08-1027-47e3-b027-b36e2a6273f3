{"name": "@procuregpt/api", "version": "1.0.0", "description": "ProcureGPT API Server", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "devDependencies": {"typescript": "^5.3.3", "tsx": "^4.7.0", "vitest": "^1.2.0", "@types/node": "^20.11.0"}, "dependencies": {"@procuregpt/types": "workspace:*", "@procuregpt/config": "workspace:*", "@procuregpt/shared": "workspace:*", "@procuregpt/database": "workspace:*", "@procuregpt/workflow-engine": "workspace:*", "@procuregpt/email-service": "workspace:*", "@hono/node-server": "^1.8.2", "hono": "^4.0.0", "tsyringe": "^4.8.0", "reflect-metadata": "^0.2.1", "zod": "^3.25.67", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1"}}