#!/usr/bin/env tsx

/**
 * <PERSON><PERSON> script to test the job queue system
 * This script demonstrates how workflows are now executed asynchronously
 */

import 'reflect-metadata';
import { loadConfig } from '@procuregpt/config';
import { diContainer, TOKENS } from '@procuregpt/shared';
import type { JobQueue, WorkflowExecutionJobData } from '@procuregpt/shared';

async function demoJobQueue() {
  console.log('🚀 ProcureGPT Job Queue Demo\n');

  try {
    // Load configuration
    const config = loadConfig();
    
    // Initialize DI container
    await diContainer.initializeAll(config);
    
    // Get job queue
    const jobQueue = diContainer.resolve<JobQueue>(TOKENS.JOB_QUEUE);
    
    console.log('✅ Job queue initialized');
    
    // Create a sample workflow execution job
    const jobData: WorkflowExecutionJobData = {
      id: `demo-${Date.now()}`,
      type: 'workflow_execution',
      workflowId: 'procurement-agent-v2',
      input: {
        query: 'Find sustainable packaging suppliers in Mangaluru under $5,000 budget with ESG compliance',
        category: 'packaging',
        budget: 5000,
        region: 'Mangaluru, India',
      },
      variables: {
        esg_required: true,
        max_budget: 5000,
      },
      executionId: `exec-demo-${Date.now()}`,
      priority: 'high',
    };

    console.log('📝 Adding workflow job to queue...');
    const job = await jobQueue.add(jobData, {
      priority: 'high',
    });

    console.log(`✅ Job added with ID: ${job.id}`);
    console.log(`📊 Job status: ${await jobQueue.getJobStatus(job.id!)}`);

    // Monitor job progress
    console.log('\n🔄 Monitoring job progress...');
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds max

    while (!completed && attempts < maxAttempts) {
      const status = await jobQueue.getJobStatus(job.id!);
      const progress = await jobQueue.getJobProgress(job.id!);
      
      console.log(`📈 Status: ${status}, Progress: ${progress?.percentage || 0}%`);
      
      if (progress?.message) {
        console.log(`💬 Message: ${progress.message}`);
      }

      if (status === 'completed' || status === 'failed') {
        completed = true;
        
        if (status === 'completed') {
          const result = await jobQueue.getJobResult(job.id!);
          console.log('\n✅ Job completed successfully!');
          console.log('📋 Result:', JSON.stringify(result, null, 2));
        } else {
          console.log('\n❌ Job failed');
          const result = await jobQueue.getJobResult(job.id!);
          console.log('💥 Error:', result?.error);
        }
      } else {
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      }
    }

    if (!completed) {
      console.log('\n⏰ Job monitoring timed out');
    }

    // Show queue stats
    console.log('\n📊 Queue Statistics:');
    const stats = await jobQueue.getQueueStats();
    console.log(`   Waiting: ${stats.waiting}`);
    console.log(`   Active: ${stats.active}`);
    console.log(`   Completed: ${stats.completed}`);
    console.log(`   Failed: ${stats.failed}`);
    console.log(`   Delayed: ${stats.delayed}`);
    console.log(`   Paused: ${stats.paused}`);

    // Clean up
    await jobQueue.close();
    console.log('\n🔚 Demo completed');

  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the demo
demoJobQueue();
