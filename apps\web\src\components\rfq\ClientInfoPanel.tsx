import { Card, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { PartyInfo } from "@/store/rfqStore";

export function ClientInfoPanel({ value, onChange }: { value: PartyInfo; onChange: (v: PartyInfo) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Client / Vendor Info</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Company</Label>
          <Input value={value.company} onChange={e => onChange({ ...value, company: e.target.value })} placeholder="Company" />
        </div>
        <div>
          <Label>Contact Name</Label>
          <Input value={value.contactName || ""} onChange={e => onChange({ ...value, contactName: e.target.value })} placeholder="Contact" />
        </div>
        <div>
          <Label>Email</Label>
          <Input type="email" value={value.email || ""} onChange={e => onChange({ ...value, email: e.target.value })} placeholder="<EMAIL>" />
        </div>
        <div>
          <Label>Phone</Label>
          <Input value={value.phone || ""} onChange={e => onChange({ ...value, phone: e.target.value })} placeholder="+91 ..." />
        </div>
        <div className="md:col-span-2">
          <Label>Address</Label>
          <Input value={value.address || ""} onChange={e => onChange({ ...value, address: e.target.value })} placeholder="Street, City, Country" />
        </div>
      </CardContent>
    </Card>
  );
}

