import { Hono } from 'hono';
import { z } from 'zod';
import { readState } from './storage';

const app = new Hono();

// Email configuration schema
const EmailConfigSchema = z.object({
  smtp: z.object({
    host: z.string(),
    port: z.number(),
    secure: z.boolean(),
    auth: z.object({
      user: z.string(),
      pass: z.string(),
    }),
  }),
  from: z.object({
    name: z.string(),
    email: z.string().email(),
  }),
});

// Email data schema
const EmailDataSchema = z.object({
  rfqId: z.string(),
  recipients: z.array(z.string().email()),
  subject: z.string(),
  message: z.string(),
  includeAttachments: z.boolean(),
});

// Email template generator
function generateRFQEmailHTML(rfq: any, message: string): string {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request for Quotation - ${rfq.refNumber}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .rfq-details {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .line-item {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            font-size: 14px;
            color: #666;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f5f5f5;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Request for Quotation</h1>
        <p><strong>RFQ Number:</strong> ${rfq.refNumber}</p>
        <p><strong>Date:</strong> ${formatDate(rfq.createdAt)}</p>
        ${rfq.responseDeadline ? `<p><strong>Response Deadline:</strong> ${formatDate(rfq.responseDeadline)}</p>` : ''}
    </div>

    <div class="message">
        ${message.split('\n').map(line => `<p>${line}</p>`).join('')}
    </div>

    <div class="rfq-details">
        <h2>${rfq.title}</h2>
        ${rfq.description ? `<p>${rfq.description}</p>` : ''}

        ${rfq.client ? `
        <h3>Client Information</h3>
        <table>
            ${rfq.client.company ? `<tr><td><strong>Company:</strong></td><td>${rfq.client.company}</td></tr>` : ''}
            ${rfq.client.contactName ? `<tr><td><strong>Contact:</strong></td><td>${rfq.client.contactName}</td></tr>` : ''}
            ${rfq.client.email ? `<tr><td><strong>Email:</strong></td><td>${rfq.client.email}</td></tr>` : ''}
            ${rfq.client.phone ? `<tr><td><strong>Phone:</strong></td><td>${rfq.client.phone}</td></tr>` : ''}
        </table>
        ` : ''}

        ${rfq.lineItems && rfq.lineItems.length > 0 ? `
        <h3>Product Specifications</h3>
        ${rfq.lineItems.map((item: any, index: number) => `
        <div class="line-item">
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <span class="badge">Item #${index + 1}</span>
                <strong>${item.itemName}</strong>
            </div>
            ${item.partNumber ? `<p><strong>Part Number:</strong> ${item.partNumber}</p>` : ''}
            <p><strong>Quantity:</strong> ${item.quantity} ${item.unit || 'units'}</p>
            ${item.dueDate ? `<p><strong>Due Date:</strong> ${formatDate(item.dueDate)}</p>` : ''}
            ${item.materialSpec ? `<p><strong>Material Specification:</strong> ${item.materialSpec}</p>` : ''}
            ${item.notes ? `<p><strong>Notes:</strong> ${item.notes}</p>` : ''}
        </div>
        `).join('')}
        ` : ''}

        ${rfq.notes ? `
        <h3>Additional Notes</h3>
        <p>${rfq.notes}</p>
        ` : ''}

        ${rfq.attachments && rfq.attachments.length > 0 ? `
        <h3>Attachments</h3>
        <ul>
            ${rfq.attachments.map((att: any) => `<li>${att.name} ${att.type ? `(${att.type})` : ''}</li>`).join('')}
        </ul>
        ` : ''}
    </div>

    <div class="footer">
        <p>This is an automated email from the Procurement Assistant system.</p>
        <p>Please respond with your quotation by the specified deadline.</p>
    </div>
</body>
</html>
  `.trim();
}

// Email sending function with SMTP support
async function sendEmail(to: string[], subject: string, html: string, text: string): Promise<void> {
  const smtpHost = process.env.SMTP_HOST;
  const smtpUser = process.env.SMTP_USER;
  const smtpPass = process.env.SMTP_PASS;

  // Check if SMTP is configured
  if (smtpHost && smtpUser && smtpPass) {
    try {
      // In a real implementation, you would use nodemailer here
      // For now, we'll simulate the SMTP sending
      console.log('📧 SMTP Configuration detected - would send via SMTP');
      console.log('📧 SMTP Host:', smtpHost);
      console.log('📧 SMTP User:', smtpUser);
      console.log('📧 Recipients:', to.join(', '));
      console.log('📧 Subject:', subject);

      // Simulate SMTP sending delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // TODO: Replace with actual nodemailer implementation
      /*
      const nodemailer = require('nodemailer');
      const transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      await transporter.sendMail({
        from: {
          name: process.env.EMAIL_FROM_NAME || 'Procurement Assistant',
          address: process.env.EMAIL_FROM_ADDRESS || process.env.SMTP_USER,
        },
        to: to.join(', '),
        subject,
        html,
        text,
      });
      */

    } catch (error) {
      console.error('SMTP sending failed:', error);
      throw new Error('Failed to send email via SMTP');
    }
  } else {
    // Fallback to mock/development mode
    console.log('📧 No SMTP configuration - using development mode');
    console.log('📧 Email would be sent to:', to.join(', '));
    console.log('📧 Subject:', subject);
    console.log('📧 HTML length:', html.length);

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Convert HTML to plain text (basic implementation)
function htmlToText(html: string): string {
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim();
}

// Email sending endpoint
app.post('/api/email/send-rfq', async (c) => {
  try {
    const body = await c.req.json();
    const emailData = EmailDataSchema.parse(body);

    // Get RFQ data from storage
    const state = await readState();
    const rfq = state.rfqs.find(r => r.id === emailData.rfqId);

    if (!rfq) {
      return c.json({ error: 'RFQ not found' }, 404);
    }

    // Generate email content
    const htmlContent = generateRFQEmailHTML(rfq, emailData.message);
    const textContent = htmlToText(htmlContent);

    // Send email
    await sendEmail(emailData.recipients, emailData.subject, htmlContent, textContent);

    return c.json({
      success: true,
      message: `Email sent to ${emailData.recipients.length} recipient(s)`,
      recipients: emailData.recipients,
    });

  } catch (error) {
    console.error('Email sending error:', error);
    
    if (error instanceof z.ZodError) {
      return c.json({ 
        error: 'Invalid email data', 
        details: error.errors 
      }, 400);
    }

    return c.json({ 
      error: error instanceof Error ? error.message : 'Failed to send email' 
    }, 500);
  }
});

// Email configuration endpoint (for testing)
app.get('/api/email/config', async (c) => {
  const config = {
    configured: !!(process.env.SMTP_HOST && process.env.SMTP_USER),
    from: {
      name: process.env.EMAIL_FROM_NAME || 'Procurement Assistant',
      email: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
    },
  };

  return c.json(config);
});

export { app as emailRoutes };
