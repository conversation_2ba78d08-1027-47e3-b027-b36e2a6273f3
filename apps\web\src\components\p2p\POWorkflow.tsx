import { useMemo, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { PurchaseOrder, PurchaseOrderLine, UUID } from "@/types/p2p";

export type POWorkflowProps = {
  purchaseOrders: PurchaseOrder[];
  onCreatePO: (po: PurchaseOrder) => void;
  onStatusChange: (id: UUID, status: PurchaseOrder["status"]) => void;
};

export function POWorkflow({ purchaseOrders, onCreatePO, onStatusChange }: POWorkflowProps) {
  const [form, setForm] = useState<{ vendorId: string; currency: string; totalAmount: number }>(() => ({ vendorId: "", currency: "INR", totalAmount: 0 }));

  function handleCreate(e: React.FormEvent) {
    e.preventDefault();
    if (!form.vendorId) return;
    const po: PurchaseOrder = {
      id: crypto.randomUUID(),
      vendorId: form.vendorId,
      lines: [],
      status: "pending",
      deliveryDate: undefined,
      totalAmount: form.totalAmount,
      currency: form.currency,
    };
    onCreatePO(po);
    setForm({ vendorId: "", currency: "INR", totalAmount: 0 });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Purchase Orders</CardTitle>
        <CardDescription>Create and track PO status</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleCreate} className="grid grid-cols-1 md:grid-cols-5 gap-2">
          <Input placeholder="Vendor ID" value={form.vendorId} onChange={e => setForm({ ...form, vendorId: e.target.value })} />
          <Input placeholder="Currency" value={form.currency} onChange={e => setForm({ ...form, currency: e.target.value })} />
          <Input type="number" step="0.01" placeholder="Total Amount" value={form.totalAmount} onChange={e => setForm({ ...form, totalAmount: Number(e.target.value) })} />
          <Button type="submit" className="md:col-span-1">Create PO</Button>
        </form>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {purchaseOrders.map(po => (
            <div className="border rounded-md p-3" key={po.id}>
              <div className="flex items-center justify-between">
                <div className="font-medium">PO #{po.id.slice(0, 8)}</div>
                <select value={po.status} onChange={e => onStatusChange(po.id, e.target.value as any)} className="border rounded-md px-2 py-1 bg-transparent">
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="sent">Sent</option>
                  <option value="acknowledged">Acknowledged</option>
                </select>
              </div>
              <div className="text-xs text-muted-foreground">Vendor: {po.vendorId} • Amount: {po.totalAmount} {po.currency}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

