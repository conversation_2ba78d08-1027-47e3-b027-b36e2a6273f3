import { z } from 'zod';

// Workflow Node Types
export const WorkflowNodeTypeSchema = z.enum([
  'call_llm',
  'semantic_search',
  'send_email',
  'human_approval',
  'data_transform',
  'conditional',
  'parallel',
  'webhook',
  'database_query',
  'file_upload'
]);

export type WorkflowNodeType = z.infer<typeof WorkflowNodeTypeSchema>;

// Workflow Node Definition
export const WorkflowNodeSchema = z.object({
  id: z.string(),
  type: WorkflowNodeTypeSchema,
  name: z.string(),
  description: z.string().optional(),
  config: z.record(z.any()),
  dependencies: z.array(z.string()).default([]),
  timeout: z.number().optional(),
  retries: z.number().default(0),
  condition: z.string().optional(), // JavaScript expression for conditional execution
});

export type WorkflowNode = z.infer<typeof WorkflowNodeSchema>;

// Workflow Definition
export const WorkflowDefinitionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  nodes: z.array(WorkflowNodeSchema),
  variables: z.record(z.any()).default({}),
  metadata: z.record(z.any()).default({}),
});

export type WorkflowDefinition = z.infer<typeof WorkflowDefinitionSchema>;

// Workflow Execution Status
export const WorkflowExecutionStatusSchema = z.enum([
  'pending',
  'running',
  'paused',
  'completed',
  'failed',
  'cancelled'
]);

export type WorkflowExecutionStatus = z.infer<typeof WorkflowExecutionStatusSchema>;

// Workflow Node Execution Status
export const NodeExecutionStatusSchema = z.enum([
  'pending',
  'running',
  'completed',
  'failed',
  'skipped',
  'waiting_approval'
]);

export type NodeExecutionStatus = z.infer<typeof NodeExecutionStatusSchema>;

// Workflow Execution Context
export const WorkflowExecutionContextSchema = z.object({
  workflowId: z.string(),
  executionId: z.string(),
  userId: z.string().optional(),
  input: z.record(z.any()),
  variables: z.record(z.any()).default({}),
  nodeResults: z.record(z.any()).default({}),
  startTime: z.date(),
  endTime: z.date().optional(),
  status: WorkflowExecutionStatusSchema,
  error: z.string().optional(),
});

export type WorkflowExecutionContext = z.infer<typeof WorkflowExecutionContextSchema>;

// Node Execution Result
export const NodeExecutionResultSchema = z.object({
  nodeId: z.string(),
  status: NodeExecutionStatusSchema,
  result: z.any().optional(),
  error: z.string().optional(),
  startTime: z.date(),
  endTime: z.date().optional(),
  executionTime: z.number().optional(),
  metadata: z.record(z.any()).default({}),
});

export type NodeExecutionResult = z.infer<typeof NodeExecutionResultSchema>;

// Human Approval Request
export const HumanApprovalRequestSchema = z.object({
  id: z.string(),
  workflowExecutionId: z.string(),
  nodeId: z.string(),
  title: z.string(),
  description: z.string(),
  data: z.record(z.any()),
  requiredApprovers: z.array(z.string()).default([]),
  approvers: z.array(z.string()).default([]),
  status: z.enum(['pending', 'approved', 'rejected']),
  createdAt: z.date(),
  resolvedAt: z.date().optional(),
  resolvedBy: z.string().optional(),
  comments: z.string().optional(),
});

export type HumanApprovalRequest = z.infer<typeof HumanApprovalRequestSchema>;
