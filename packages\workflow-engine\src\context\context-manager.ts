import type { WorkflowExecutionContext } from '@procuregpt/types';

/**
 * Manages workflow execution contexts in memory
 * In production, this should be backed by a persistent store (Redis, Database)
 */
export class WorkflowContextManager {
  private contexts = new Map<string, WorkflowExecutionContext>();

  setContext(executionId: string, context: WorkflowExecutionContext): void {
    this.contexts.set(executionId, { ...context });
  }

  getContext(executionId: string): WorkflowExecutionContext | undefined {
    const context = this.contexts.get(executionId);
    return context ? { ...context } : undefined;
  }

  deleteContext(executionId: string): boolean {
    return this.contexts.delete(executionId);
  }

  getAllContexts(): WorkflowExecutionContext[] {
    return Array.from(this.contexts.values()).map(context => ({ ...context }));
  }

  getContextsByStatus(status: string): WorkflowExecutionContext[] {
    return Array.from(this.contexts.values())
      .filter(context => context.status === status)
      .map(context => ({ ...context }));
  }

  getContextsByUser(userId: string): WorkflowExecutionContext[] {
    return Array.from(this.contexts.values())
      .filter(context => context.userId === userId)
      .map(context => ({ ...context }));
  }

  cleanup(olderThanMs: number = 24 * 60 * 60 * 1000): number {
    const cutoff = new Date(Date.now() - olderThanMs);
    let cleaned = 0;

    for (const [executionId, context] of this.contexts.entries()) {
      if (context.startTime < cutoff && 
          (context.status === 'completed' || context.status === 'failed' || context.status === 'cancelled')) {
        this.contexts.delete(executionId);
        cleaned++;
      }
    }

    return cleaned;
  }
}
