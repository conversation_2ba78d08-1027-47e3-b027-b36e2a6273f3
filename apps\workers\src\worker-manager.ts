import { diContainer, TOKENS } from '@procuregpt/shared';
import type { JobProcessor, Logger } from '@procuregpt/shared';
import { WorkflowJobHandler } from './handlers/workflow-handler';
import { Email<PERSON>obHandler } from './handlers/email-handler';
import { DocumentProcessingJobHandler } from './handlers/document-handler';
import { BulkRFQJobHandler } from './handlers/bulk-rfq-handler';

export class WorkerManager {
  private jobProcessor: JobProcessor;
  private logger: Logger;
  private handlers: Array<{ type: string; handler: any }> = [];

  constructor() {
    this.jobProcessor = diContainer.resolve<JobProcessor>(TOKENS.JOB_PROCESSOR);
    this.logger = diContainer.resolve<Logger>(TOKENS.LOGGER);
  }

  async start(): Promise<void> {
    this.logger.info('Starting worker manager...');

    // Register job handlers
    await this.registerHandlers();

    // Start the job processor
    await this.jobProcessor.start();

    this.logger.info('Worker manager started successfully', {
      registeredHandlers: this.handlers.length
    });
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping worker manager...');

    // Stop the job processor
    await this.jobProcessor.stop();

    this.logger.info('Worker manager stopped');
  }

  private async registerHandlers(): Promise<void> {
    // Workflow execution handler
    const workflowHandler = new WorkflowJobHandler();
    this.jobProcessor.registerHandler('workflow_execution', workflowHandler.handle.bind(workflowHandler));
    this.handlers.push({ type: 'workflow_execution', handler: workflowHandler });

    // Email handler
    const emailHandler = new EmailJobHandler();
    this.jobProcessor.registerHandler('send_email', emailHandler.handle.bind(emailHandler));
    this.handlers.push({ type: 'send_email', handler: emailHandler });

    // Document processing handler
    const documentHandler = new DocumentProcessingJobHandler();
    this.jobProcessor.registerHandler('process_document', documentHandler.handle.bind(documentHandler));
    this.handlers.push({ type: 'process_document', handler: documentHandler });

    // Bulk RFQ handler
    const bulkRFQHandler = new BulkRFQJobHandler();
    this.jobProcessor.registerHandler('send_bulk_rfq', bulkRFQHandler.handle.bind(bulkRFQHandler));
    this.handlers.push({ type: 'send_bulk_rfq', handler: bulkRFQHandler });

    this.logger.info('Job handlers registered', {
      handlers: this.handlers.map(h => h.type)
    });
  }
}
