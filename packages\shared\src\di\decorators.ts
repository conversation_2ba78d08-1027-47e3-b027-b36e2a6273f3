import { injectable, inject, singleton } from 'tsyringe';
import { TokenType } from './tokens';

/**
 * Service decorator - marks a class as injectable
 */
export const Service = injectable;

/**
 * Singleton decorator - marks a service as singleton
 */
export const Singleton = singleton;

/**
 * Inject decorator - injects a dependency by token
 */
export const Inject = (token: TokenType) => inject(token);

/**
 * Repository decorator - marks a class as a repository (singleton by default)
 */
export function Repository(target: any) {
  return singleton()(injectable()(target));
}

/**
 * Controller decorator - marks a class as a controller
 */
export function Controller(target: any) {
  return injectable()(target);
}

/**
 * Middleware decorator - marks a class as middleware
 */
export function Middleware(target: any) {
  return injectable()(target);
}
