{"name": "@procuregpt/database", "version": "1.0.0", "description": "Database services and RAG implementation for ProcureGPT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage", "migrate": "tsx src/migrations/run.ts", "seed": "tsx src/seeds/run.ts"}, "devDependencies": {"typescript": "^5.3.3", "tsx": "^4.7.0", "vitest": "^1.2.0", "@types/node": "^20.11.0", "@types/pg": "^8.10.9"}, "dependencies": {"@procuregpt/types": "workspace:*", "@procuregpt/config": "workspace:*", "@procuregpt/shared": "workspace:*", "pg": "^8.11.3", "pg-pool": "^3.6.1", "tsyringe": "^4.8.0", "reflect-metadata": "^0.2.1", "zod": "^3.25.67", "openai": "^4.28.0"}}