/**
 * Custom Error Classes for ProcureGPT
 */

export class ProcureGPTError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(message: string, code: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends ProcureGPTError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    if (field) {
      this.message = `${field}: ${message}`;
    }
  }
}

export class AuthenticationError extends ProcureGPTError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTHENTICATION_ERROR', 401);
  }
}

export class AuthorizationError extends ProcureGPTError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403);
  }
}

export class NotFoundError extends ProcureGPTError {
  constructor(resource: string, id?: string) {
    const message = id ? `${resource} with id ${id} not found` : `${resource} not found`;
    super(message, 'NOT_FOUND', 404);
  }
}

export class ConflictError extends ProcureGPTError {
  constructor(message: string) {
    super(message, 'CONFLICT', 409);
  }
}

export class WorkflowError extends ProcureGPTError {
  constructor(message: string, workflowId?: string) {
    super(message, 'WORKFLOW_ERROR', 500);
    if (workflowId) {
      this.message = `Workflow ${workflowId}: ${message}`;
    }
  }
}

export class ExternalServiceError extends ProcureGPTError {
  constructor(service: string, message: string) {
    super(`${service}: ${message}`, 'EXTERNAL_SERVICE_ERROR', 502);
  }
}

export class RateLimitError extends ProcureGPTError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_ERROR', 429);
  }
}

export class DatabaseError extends ProcureGPTError {
  constructor(message: string, operation?: string) {
    const fullMessage = operation ? `Database ${operation}: ${message}` : message;
    super(fullMessage, 'DATABASE_ERROR', 500);
  }
}

/**
 * Error handler utility
 */
export function isOperationalError(error: Error): boolean {
  if (error instanceof ProcureGPTError) {
    return error.isOperational;
  }
  return false;
}

/**
 * Convert unknown error to ProcureGPTError
 */
export function toProcureGPTError(error: unknown): ProcureGPTError {
  if (error instanceof ProcureGPTError) {
    return error;
  }
  
  if (error instanceof Error) {
    return new ProcureGPTError(error.message, 'UNKNOWN_ERROR', 500, false);
  }
  
  return new ProcureGPTError(String(error), 'UNKNOWN_ERROR', 500, false);
}
