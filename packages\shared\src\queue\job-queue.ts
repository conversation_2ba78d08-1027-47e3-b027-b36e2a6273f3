import { Queue, QueueOptions, Job } from 'bullmq';
import Redis from 'ioredis';
import { Service, Singleton, Inject, TOKENS } from '../di';
import type { Logger } from '../logger';
import type { JobData, JobOptions, JobResult, JobProgress } from './job-types';

export interface JobQueue {
  add<T extends JobData>(jobData: T, options?: JobOptions): Promise<Job<T>>;
  getJob(jobId: string): Promise<Job | null>;
  getJobStatus(jobId: string): Promise<string | null>;
  getJobProgress(jobId: string): Promise<JobProgress | null>;
  getJobResult(jobId: string): Promise<JobResult | null>;
  removeJob(jobId: string): Promise<void>;
  pauseQueue(): Promise<void>;
  resumeQueue(): Promise<void>;
  getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  }>;
  close(): Promise<void>;
}

@Singleton()
@Service
export class BullMQJobQueue implements JobQueue {
  private queue: Queue;
  private redis: Redis;

  constructor(
    @Inject(TOKENS.CONFIG) private config: { redisUrl?: string; queueName: string },
    @Inject(TOKENS.LOGGER) private logger: Logger
  ) {
    // Initialize Redis connection
    this.redis = new Redis(config.redisUrl || 'redis://localhost:6379', {
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      lazyConnect: true,
    });

    // Initialize BullMQ Queue
    const queueOptions: QueueOptions = {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    };

    this.queue = new Queue(config.queueName, queueOptions);

    // Set up event listeners
    this.setupEventListeners();

    this.logger.info('BullMQ job queue initialized', {
      queueName: config.queueName,
      redisUrl: config.redisUrl ? '[REDACTED]' : 'default'
    });
  }

  async add<T extends JobData>(jobData: T, options: JobOptions = {}): Promise<Job<T>> {
    try {
      const job = await this.queue.add(jobData.type, jobData, {
        priority: this.mapPriorityToNumber(options.priority || 'normal'),
        delay: options.delay,
        attempts: options.attempts || 3,
        backoff: options.backoff,
        removeOnComplete: options.removeOnComplete || 100,
        removeOnFail: options.removeOnFail || 50,
        timeout: options.timeout,
      });

      this.logger.info('Job added to queue', {
        jobId: job.id,
        jobType: jobData.type,
        priority: options.priority,
        queueName: this.queue.name
      });

      return job as Job<T>;
    } catch (error) {
      this.logger.error('Failed to add job to queue', error as Error, {
        jobType: jobData.type,
        queueName: this.queue.name
      });
      throw error;
    }
  }

  async getJob(jobId: string): Promise<Job | null> {
    try {
      return await this.queue.getJob(jobId);
    } catch (error) {
      this.logger.error('Failed to get job', error as Error, { jobId });
      return null;
    }
  }

  async getJobStatus(jobId: string): Promise<string | null> {
    try {
      const job = await this.getJob(jobId);
      if (!job) return null;

      if (await job.isCompleted()) return 'completed';
      if (await job.isFailed()) return 'failed';
      if (await job.isActive()) return 'active';
      if (await job.isWaiting()) return 'waiting';
      if (await job.isDelayed()) return 'delayed';
      if (await job.isPaused()) return 'paused';

      return 'unknown';
    } catch (error) {
      this.logger.error('Failed to get job status', error as Error, { jobId });
      return null;
    }
  }

  async getJobProgress(jobId: string): Promise<JobProgress | null> {
    try {
      const job = await this.getJob(jobId);
      if (!job) return null;

      const progress = job.progress;
      if (typeof progress === 'object' && progress !== null) {
        return progress as JobProgress;
      }

      return null;
    } catch (error) {
      this.logger.error('Failed to get job progress', error as Error, { jobId });
      return null;
    }
  }

  async getJobResult(jobId: string): Promise<JobResult | null> {
    try {
      const job = await this.getJob(jobId);
      if (!job) return null;

      if (await job.isCompleted()) {
        return job.returnvalue as JobResult;
      }

      if (await job.isFailed()) {
        return {
          success: false,
          error: job.failedReason || 'Job failed',
          executionTime: 0,
          completedAt: new Date(),
        };
      }

      return null;
    } catch (error) {
      this.logger.error('Failed to get job result', error as Error, { jobId });
      return null;
    }
  }

  async removeJob(jobId: string): Promise<void> {
    try {
      const job = await this.getJob(jobId);
      if (job) {
        await job.remove();
        this.logger.info('Job removed', { jobId });
      }
    } catch (error) {
      this.logger.error('Failed to remove job', error as Error, { jobId });
      throw error;
    }
  }

  async pauseQueue(): Promise<void> {
    try {
      await this.queue.pause();
      this.logger.info('Queue paused', { queueName: this.queue.name });
    } catch (error) {
      this.logger.error('Failed to pause queue', error as Error);
      throw error;
    }
  }

  async resumeQueue(): Promise<void> {
    try {
      await this.queue.resume();
      this.logger.info('Queue resumed', { queueName: this.queue.name });
    } catch (error) {
      this.logger.error('Failed to resume queue', error as Error);
      throw error;
    }
  }

  async getQueueStats() {
    try {
      const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
        this.queue.getPaused(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: paused.length,
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', error as Error);
      throw error;
    }
  }

  async close(): Promise<void> {
    try {
      await this.queue.close();
      await this.redis.quit();
      this.logger.info('Job queue closed');
    } catch (error) {
      this.logger.error('Failed to close job queue', error as Error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    this.queue.on('completed', (job) => {
      this.logger.info('Job completed', {
        jobId: job.id,
        jobType: job.name,
        duration: job.processedOn ? job.processedOn - job.timestamp : 0
      });
    });

    this.queue.on('failed', (job, err) => {
      this.logger.error('Job failed', err, {
        jobId: job?.id,
        jobType: job?.name,
        attempts: job?.attemptsMade,
        maxAttempts: job?.opts.attempts
      });
    });

    this.queue.on('stalled', (jobId) => {
      this.logger.warn('Job stalled', { jobId });
    });

    this.queue.on('error', (error) => {
      this.logger.error('Queue error', error);
    });
  }

  private mapPriorityToNumber(priority: string): number {
    const priorityMap: Record<string, number> = {
      'low': 1,
      'normal': 5,
      'high': 10,
      'critical': 20,
    };
    return priorityMap[priority] || 5;
  }
}
